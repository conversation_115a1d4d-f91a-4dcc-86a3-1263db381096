# Project Brief: BMAD Agent FastMCP Service v2.0 - Automated Greenfield Workflow

## Executive Summary

**BMAD Agent FastMCP Service v2.0** 是对现有 FastMCP 智能体服务的重大升级，旨在实现真正的自动化 Greenfield 项目开发工作流程。该项目将在现有的 10 个专业智能体和 25+ MCP 工具基础上，添加智能化的工作流程自动化功能，使得从 PRD、Architecture、Brief 三个核心文档开始，能够自动执行文档分片、Epic 创建、Story 生成、开发实施和质量保证的完整循环，真正实现"一键式"项目开发体验。

## Problem Statement

**当前痛点：** 现有的 BMAD Agent FastMCP Service 虽然提供了完整的智能体生态系统，但工作流程推进仍需要大量手动干预。开发者需要：

1. **手动推进工作流程步骤** - 从文档分片到 Story 创建，每个阶段都需要人工判断和操作
2. **重复性的上下文切换** - 在不同智能体间切换时需要重新加载相关文档和上下文
3. **状态跟踪困难** - 缺乏自动化的进度跟踪，容易遗漏步骤或重复工作
4. **任务执行仅为模拟** - 当前的任务执行只返回模拟结果，无法真正生成代码或修改文件

**影响量化：** 一个典型的 Greenfield 项目需要 15-20 次手动智能体切换和状态确认，每次切换平均耗时 3-5 分钟，总计增加 45-100 分钟的非生产性时间。

**现有解决方案的不足：** 市面上的 AI 开发工具要么只专注于代码生成，要么缺乏完整的项目管理工作流程。没有一个解决方案能够从项目文档开始，自动化地完成整个开发生命周期。

## Proposed Solution

**核心解决方案：** 构建智能化的工作流程自动化引擎，实现从文档到代码的端到端自动化。

**关键特性：**

1. **智能工作流程引擎** - 自动检测当前阶段，验证前置条件，智能推进到下一步骤
2. **文档自动分片系统** - 智能解析 PRD 和 Architecture 文档，自动生成结构化的文档片段
3. **Epic/Story 自动生成** - 基于分片文档自动创建 Epic 和详细的 Story 文件
4. **真实任务执行引擎** - 将模拟执行升级为真实的代码生成和文件操作
5. **智能体协作机制** - SM、Dev、QA 智能体间的自动协作和上下文共享

**差异化优势：**
- 首个真正实现端到端自动化的 AI 开发工作流程
- 基于成熟的 BMAD 方法论，确保最佳实践
- 与 Cursor IDE 深度集成，提供无缝开发体验

## Target Users

### Primary User Segment: BMAD 方法论采用者

**用户画像：**
- 已经熟悉 BMAD 方法论的开发团队和个人开发者
- 使用 Cursor IDE 进行 AI 辅助开发
- 希望提升项目开发效率和自动化程度

**当前行为：**
- 手动执行 BMAD 工作流程的各个步骤
- 在不同智能体间频繁切换
- 花费大量时间在工作流程管理上而非核心开发

**核心需求：**
- 减少手动操作，提升开发效率
- 自动化的进度跟踪和状态管理
- 真实的代码生成和项目管理能力

### Secondary User Segment: AI 开发工具探索者

**用户画像：**
- 对 AI 辅助开发感兴趣的开发者
- 寻找完整的项目开发解决方案
- 希望学习和采用最佳开发实践

## Goals & Success Metrics

### Business Objectives
- 将手动工作流程操作时间减少 80%（从 45-100 分钟降至 10-20 分钟）
- 提升 BMAD 方法论的采用率 50%
- 在 6 个月内获得 1000+ 活跃用户

### User Success Metrics
- 用户完成一个完整 Greenfield 项目的时间减少 60%
- 工作流程错误率降低 90%
- 用户满意度评分达到 4.5/5.0

### Key Performance Indicators (KPIs)
- **自动化率**: 工作流程步骤的自动化执行比例 > 85%
- **任务成功率**: 自动生成的代码和文档的质量评分 > 4.0/5.0
- **用户留存率**: 30 天活跃用户留存率 > 70%

## MVP Scope

### Core Features (Must Have)
- **自动工作流程引擎**: 检测状态、验证条件、自动推进工作流程
- **文档自动分片**: PRD 和 Architecture 文档的智能分片功能
- **Story 自动生成**: 基于 Epic 和架构文档自动创建详细 Story
- **真实任务执行**: 将现有模拟执行升级为真实的文件操作和代码生成
- **基础状态跟踪**: 项目进度和当前阶段的可视化展示

### Out of Scope for MVP
- 复杂的项目模板系统
- 多项目并行管理
- 高级的自定义工作流程配置
- 团队协作功能
- 详细的分析和报告功能

### MVP Success Criteria
用户能够从 docs/ 文件夹中的三个核心文档（brief.md, prd.md, architecture.md）开始，通过一个命令启动完整的自动化开发流程，并成功生成第一个可工作的 Story 实现。

## Post-MVP Vision

### Phase 2 Features
- 多项目管理和模板系统
- 高级工作流程自定义
- 团队协作和权限管理
- 详细的项目分析和报告

### Long-term Vision
成为 AI 驱动开发的标准工作流程平台，支持从概念到部署的完整自动化开发生命周期。

### Expansion Opportunities
- 支持更多 IDE 集成（VS Code, WebStorm 等）
- 扩展到其他开发方法论（Scrum, Kanban 等）
- 企业级功能和私有部署选项

## Technical Considerations

### Platform Requirements
- **Target Platforms**: Cursor IDE (主要), VS Code (未来)
- **Browser/OS Support**: macOS, Windows, Linux
- **Performance Requirements**: 工作流程步骤响应时间 < 5 秒

### Technology Preferences
- **Frontend**: 继续使用 FastMCP 框架
- **Backend**: Python 3.8+, 保持现有技术栈
- **Database**: 文件系统存储 + SQLite（状态跟踪）
- **Hosting/Infrastructure**: 本地运行，无需云服务

### Architecture Considerations
- **Repository Structure**: 保持现有结构，扩展 MCP 工具
- **Service Architecture**: 单体应用，专注于本地性能
- **Integration Requirements**: 深度集成 Cursor IDE MCP 协议
- **Security/Compliance**: 本地数据处理，无隐私风险

## Constraints & Assumptions

### Constraints
- **Budget**: 开源项目，无预算限制
- **Timeline**: 4-6 周完成 MVP
- **Resources**: 单人开发，利用现有代码基础
- **Technical**: 必须与现有 FastMCP 架构兼容

### Key Assumptions
- 用户已经熟悉 BMAD 方法论基础概念
- 用户有完整的项目文档作为起点
- Cursor IDE 的 MCP 支持保持稳定
- 现有的智能体配置和模板质量足够高

## Risks & Open Questions

### Key Risks
- **技术复杂度**: 自动化工作流程的复杂性可能超出预期
- **用户采用**: 用户可能更喜欢手动控制而非完全自动化
- **质量保证**: 自动生成的代码和文档质量可能不稳定

### Open Questions
- 如何平衡自动化程度和用户控制权？
- 工作流程出错时的恢复机制如何设计？
- 如何确保自动生成内容的质量一致性？

### Areas Needing Further Research
- 用户对自动化程度的真实偏好
- 现有 BMAD 用户的具体痛点优先级
- 技术实现的最佳架构模式

## Next Steps

### Immediate Actions
1. 创建详细的 PRD 文档，定义具体功能需求
2. 设计技术架构，确定实现方案
3. 开始 MVP 开发，优先实现核心自动化功能

### PM Handoff
这个项目简介为 BMAD Agent FastMCP Service v2.0 提供了完整的背景信息。请开始 PRD 创建模式，基于这个简介与用户协作创建详细的产品需求文档，重点关注自动化工作流程的具体功能定义和用户交互设计。
