# BMAD Agent FastMCP Service v2.0 Architecture Document

## Introduction

This document outlines the overall project architecture for BMAD Agent FastMCP Service v2.0, including backend systems, shared services, and automation workflow components. Its primary goal is to serve as the guiding architectural blueprint for AI-driven development, ensuring consistency and adherence to chosen patterns and technologies.

**Relationship to Frontend Architecture:**
This project primarily focuses on backend MCP service architecture with minimal UI components. The main interface is through Cursor IDE's MCP protocol integration.

### Starter Template or Existing Project

**Existing Project Foundation:** This project builds upon the existing BMAD Agent FastMCP Service codebase. The current foundation includes:
- FastMCP framework integration
- 10 configured intelligent agents
- 25+ existing MCP tools
- Dual LLM mode support (Cursor built-in + DeepSeek API)
- Complete .bmad-core configuration structure

**Analysis of Existing Architecture:**
- **Technology Stack:** Python 3.8+, FastMCP framework, YAML configuration
- **Project Structure:** Monolithic service with modular MCP tool organization
- **Architectural Patterns:** Tool-based service architecture, agent configuration system
- **Constraints:** Must maintain backward compatibility with existing MCP tools and agent configurations

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2024-12-19 | 1.0 | Initial Architecture Document | Alex (Architect) |

## High Level Architecture

### Technical Summary

The system follows an enhanced monolithic service architecture built on the FastMCP framework, extending the existing tool-based pattern with intelligent workflow automation capabilities. The core enhancement introduces a workflow state management engine that orchestrates document processing, agent collaboration, and task execution in an automated pipeline. The architecture maintains full backward compatibility while adding sophisticated automation layers that transform manual processes into intelligent, self-managing workflows.

### High Level Overview

**Architectural Style:** Enhanced Monolithic Service with Workflow Automation Engine
**Repository Structure:** Monorepo (existing structure maintained)
**Service Architecture:** Single Python service with modular MCP tool extensions
**Primary Flow:** Document → Workflow Engine → Agent Orchestration → Task Execution → Code Generation

**Key Architectural Decisions:**
1. **Extend vs Rebuild:** Enhance existing FastMCP service rather than rebuild
2. **Workflow Engine:** Central orchestration component for automation
3. **State Management:** SQLite-based local state tracking
4. **Agent Collaboration:** Event-driven communication between agents
5. **Backward Compatibility:** All existing tools and agents remain functional

### High Level Project Diagram

```mermaid
graph TD
    A[docs/ Folder<br/>brief.md, prd.md, architecture.md] --> B[Document Processor]
    B --> C[Workflow State Engine]
    C --> D[Agent Orchestrator]
    D --> E[SM Agent]
    D --> F[Dev Agent]
    D --> G[QA Agent]
    E --> H[Story Generator]
    F --> I[Code Generator]
    G --> J[Code Reviewer]
    H --> K[Task Executor]
    I --> K
    J --> K
    K --> L[File System<br/>Generated Code & Docs]
    C --> M[SQLite State DB]
    N[Cursor IDE] --> O[FastMCP Protocol]
    O --> C
```

### Architectural and Design Patterns

- **Workflow Orchestration Pattern:** Central workflow engine manages state transitions and agent coordination
- **Command Pattern:** MCP tools as discrete commands with consistent interfaces
- **State Machine Pattern:** Workflow progression through defined states with validation
- **Observer Pattern:** Agents subscribe to workflow state changes for automatic activation
- **Template Method Pattern:** Consistent document processing and code generation workflows
- **Repository Pattern:** Abstract data access for state management and file operations

## Tech Stack

### Cloud Infrastructure
- **Provider:** Local Development (No cloud dependency)
- **Key Services:** File system storage, local SQLite database
- **Deployment Regions:** Local machine only

### Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Language** | Python | 3.8+ | Primary development language | Existing codebase, excellent AI/ML libraries |
| **Framework** | FastMCP | Latest | MCP service framework | Existing foundation, Cursor IDE integration |
| **Database** | SQLite | 3.40+ | Local state management | Lightweight, no setup required, perfect for local dev |
| **Document Processing** | PyYAML | 6.0+ | YAML configuration parsing | Existing dependency, handles .bmad-core configs |
| **File Operations** | Pathlib | Built-in | File system operations | Python standard library, robust path handling |
| **State Management** | Dataclasses | Built-in | Data structure definitions | Type safety, clean data models |
| **Async Processing** | asyncio | Built-in | Asynchronous workflow execution | Non-blocking agent operations |
| **Testing** | pytest | 7.4+ | Unit and integration testing | Industry standard, excellent plugin ecosystem |
| **Code Quality** | black, flake8 | Latest | Code formatting and linting | Maintain code consistency |
| **Documentation** | markdown-tree-parser | Latest | Document sharding utility | Existing BMAD tool for document processing |

## Data Models

### WorkflowState
**Purpose:** Track the current state and progress of the automated development workflow

**Key Attributes:**
- workflow_id: str - Unique identifier for the workflow instance
- current_stage: str - Current workflow stage (document_processing, epic_creation, story_development, etc.)
- project_path: str - Path to the project root directory
- documents_status: dict - Status of document processing (sharded, validated, etc.)
- epic_progress: dict - Progress tracking for each epic
- story_progress: dict - Progress tracking for each story
- created_at: datetime - Workflow creation timestamp
- updated_at: datetime - Last update timestamp

**Relationships:**
- One-to-many with StoryState
- One-to-many with TaskExecution

### StoryState
**Purpose:** Track the state and progress of individual story development

**Key Attributes:**
- story_id: str - Epic.Story identifier (e.g., "1.2")
- title: str - Story title
- status: str - Current status (draft, in_progress, review, done)
- assigned_agent: str - Currently responsible agent
- file_path: str - Path to the story markdown file
- acceptance_criteria: list - List of acceptance criteria
- tasks_completed: list - Completed development tasks
- created_at: datetime - Story creation timestamp
- completed_at: datetime - Story completion timestamp

**Relationships:**
- Many-to-one with WorkflowState
- One-to-many with TaskExecution

### TaskExecution
**Purpose:** Track individual task execution within stories

**Key Attributes:**
- task_id: str - Unique task identifier
- story_id: str - Parent story identifier
- agent_id: str - Executing agent identifier
- task_type: str - Type of task (code_generation, testing, review, etc.)
- status: str - Execution status (pending, running, completed, failed)
- input_context: dict - Task input parameters and context
- output_result: dict - Task execution results
- error_message: str - Error details if failed
- execution_time: float - Task execution duration
- created_at: datetime - Task creation timestamp

**Relationships:**
- Many-to-one with StoryState
- Many-to-one with WorkflowState

## Components

### Workflow State Engine
**Responsibility:** Central orchestration of the automated development workflow, managing state transitions and coordinating agent activities

**Key Interfaces:**
- `start_workflow(project_path: str) -> WorkflowState`
- `advance_workflow(workflow_id: str) -> WorkflowState`
- `get_workflow_status(workflow_id: str) -> WorkflowState`
- `recover_workflow(workflow_id: str, checkpoint: str) -> WorkflowState`

**Dependencies:** SQLite database, Document Processor, Agent Orchestrator

**Technology Stack:** Python asyncio, SQLite, dataclasses for state management

### Document Processor
**Responsibility:** Automated processing of project documents including sharding, validation, and Epic extraction

**Key Interfaces:**
- `shard_documents(docs_path: str) -> ShardingResult`
- `validate_document_structure(doc_path: str) -> ValidationResult`
- `extract_epics(prd_shards: list) -> list[Epic]`
- `create_epic_files(epics: list) -> list[str]`

**Dependencies:** markdown-tree-parser, PyYAML, file system

**Technology Stack:** Python pathlib, regex for markdown parsing, YAML processing

### Agent Orchestrator
**Responsibility:** Coordinate agent activation, task assignment, and inter-agent communication

**Key Interfaces:**
- `activate_agent(agent_id: str, context: dict) -> AgentSession`
- `assign_task(agent_id: str, task: Task) -> TaskExecution`
- `transfer_context(from_agent: str, to_agent: str, context: dict) -> bool`
- `get_agent_status(agent_id: str) -> AgentStatus`

**Dependencies:** Existing agent configurations, Workflow State Engine

**Technology Stack:** FastMCP agent system, Python asyncio for coordination

### Story Generator
**Responsibility:** Automated creation of detailed story files from Epic definitions and architecture context

**Key Interfaces:**
- `generate_story(epic_id: str, story_number: int) -> Story`
- `enrich_story_context(story: Story, architecture_context: dict) -> Story`
- `validate_story_completeness(story: Story) -> ValidationResult`

**Dependencies:** Document Processor (for architecture context), existing story templates

**Technology Stack:** Python template processing, YAML configuration, markdown generation

### Code Generator
**Responsibility:** Real code generation and file operations based on story requirements

**Key Interfaces:**
- `generate_code(story: Story, task: Task) -> CodeGenerationResult`
- `create_files(file_specs: list) -> list[str]`
- `modify_files(modifications: list) -> ModificationResult`
- `generate_tests(code_files: list) -> list[str]`

**Dependencies:** Story definitions, architecture specifications, coding standards

**Technology Stack:** Python AST manipulation, Jinja2 templates, file system operations

### Component Diagrams

```mermaid
graph TB
    subgraph "Workflow Engine Core"
        WSE[Workflow State Engine]
        DP[Document Processor]
        AO[Agent Orchestrator]
    end

    subgraph "Agent Layer"
        SM[SM Agent]
        DEV[Dev Agent]
        QA[QA Agent]
    end

    subgraph "Execution Layer"
        SG[Story Generator]
        CG[Code Generator]
        CR[Code Reviewer]
    end

    subgraph "Storage Layer"
        FS[File System]
        DB[(SQLite DB)]
    end

    WSE --> DP
    WSE --> AO
    WSE --> DB
    AO --> SM
    AO --> DEV
    AO --> QA
    SM --> SG
    DEV --> CG
    QA --> CR
    SG --> FS
    CG --> FS
    CR --> FS
    DP --> FS
```

## Core Workflows

```mermaid
sequenceDiagram
    participant U as User
    participant WSE as Workflow Engine
    participant DP as Document Processor
    participant SM as SM Agent
    participant DEV as Dev Agent
    participant QA as QA Agent
    participant FS as File System

    U->>WSE: start_greenfield_workflow()
    WSE->>DP: shard_documents(docs/)
    DP->>FS: Create sharded files
    DP-->>WSE: Sharding complete

    WSE->>DP: extract_epics(prd_shards)
    DP->>FS: Create epic files
    DP-->>WSE: Epics created

    loop For each Story
        WSE->>SM: generate_next_story()
        SM->>FS: Create story file
        SM-->>WSE: Story ready

        WSE->>DEV: implement_story()
        DEV->>FS: Generate code & tests
        DEV-->>WSE: Implementation complete

        WSE->>QA: review_story()
        QA->>FS: Review & refactor code
        alt QA Approves
            QA-->>WSE: Story approved
        else QA Requests Changes
            QA-->>WSE: Changes needed
            WSE->>DEV: address_feedback()
        end
    end

    WSE-->>U: Workflow complete
```

## Database Schema

```sql
-- Workflow state tracking
CREATE TABLE workflow_states (
    id TEXT PRIMARY KEY,
    current_stage TEXT NOT NULL,
    project_path TEXT NOT NULL,
    documents_status TEXT, -- JSON
    epic_progress TEXT,    -- JSON
    story_progress TEXT,   -- JSON
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Story development tracking
CREATE TABLE story_states (
    story_id TEXT PRIMARY KEY,
    workflow_id TEXT NOT NULL,
    title TEXT NOT NULL,
    status TEXT NOT NULL,
    assigned_agent TEXT,
    file_path TEXT,
    acceptance_criteria TEXT, -- JSON
    tasks_completed TEXT,     -- JSON
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_states(id)
);

-- Task execution tracking
CREATE TABLE task_executions (
    id TEXT PRIMARY KEY,
    story_id TEXT NOT NULL,
    agent_id TEXT NOT NULL,
    task_type TEXT NOT NULL,
    status TEXT NOT NULL,
    input_context TEXT,  -- JSON
    output_result TEXT,  -- JSON
    error_message TEXT,
    execution_time REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (story_id) REFERENCES story_states(story_id)
);

-- Indexes for performance
CREATE INDEX idx_workflow_stage ON workflow_states(current_stage);
CREATE INDEX idx_story_status ON story_states(status);
CREATE INDEX idx_task_status ON task_executions(status);
```

## Source Tree

```
bmad-agent-fastmcp/
├── bmad_agent_mcp.py           # Main FastMCP service (existing)
├── llm_client.py               # LLM client (existing)
├── utils.py                    # Utilities (existing)
├── requirements.txt            # Dependencies (existing)
├── .env                        # Environment config (existing)
│
├── workflow/                   # NEW: Workflow automation components
│   ├── __init__.py
│   ├── state_engine.py         # Workflow State Engine
│   ├── document_processor.py   # Document processing and sharding
│   ├── agent_orchestrator.py   # Agent coordination
│   ├── story_generator.py      # Story creation automation
│   ├── code_generator.py       # Real code generation
│   └── models.py              # Data models for workflow state
│
├── database/                   # NEW: Database management
│   ├── __init__.py
│   ├── schema.sql             # Database schema
│   ├── migrations/            # Schema migrations
│   └── connection.py          # Database connection management
│
├── .bmad-core/                # Existing BMAD configuration
│   ├── agents/                # Agent configurations (existing)
│   ├── workflows/             # Workflow definitions (existing)
│   ├── tasks/                 # Task definitions (existing)
│   └── templates/             # Document templates (existing)
│
├── docs/                      # Project documentation
│   ├── brief.md              # Project brief (NEW)
│   ├── prd.md                # Product requirements (NEW)
│   ├── architecture.md       # This document (NEW)
│   ├── prd/                  # Sharded PRD files (generated)
│   ├── architecture/         # Sharded architecture files (generated)
│   ├── epics/                # Epic files (generated)
│   └── stories/              # Story files (generated)
│
├── tests/                     # Test suite
│   ├── unit/                 # Unit tests
│   ├── integration/          # Integration tests
│   └── fixtures/             # Test data and fixtures
│
└── logs/                     # Application logs (existing)
```

## Coding Standards

### Core Standards
- **Languages & Runtimes:** Python 3.8+, strict type hints required
- **Style & Linting:** black (formatting), flake8 (linting), mypy (type checking)
- **Test Organization:** pytest with tests/ directory structure, test files named test_*.py

### Critical Rules
- **Logging Only:** Never use print() statements - use Python logging module exclusively
- **Type Safety:** All functions must have complete type hints including return types
- **Error Handling:** All file operations must include proper exception handling with specific error types
- **State Consistency:** All workflow state changes must be atomic and logged
- **Backward Compatibility:** New MCP tools must not break existing tool interfaces

## Test Strategy and Standards

### Testing Philosophy
- **Approach:** Test-driven development for new components, comprehensive test coverage for workflow automation
- **Coverage Goals:** 90% code coverage for workflow components, 80% for existing code modifications
- **Test Pyramid:** 70% unit tests, 25% integration tests, 5% end-to-end workflow tests

### Test Types and Organization

#### Unit Tests
- **Framework:** pytest 7.4+
- **File Convention:** test_*.py in tests/unit/ directory
- **Location:** tests/unit/ mirroring source structure
- **Mocking Library:** unittest.mock (built-in)
- **Coverage Requirement:** 90% for new workflow components

**AI Agent Requirements:**
- Generate tests for all public methods
- Cover edge cases and error conditions
- Follow AAA pattern (Arrange, Act, Assert)
- Mock all external dependencies (file system, database)

#### Integration Tests
- **Scope:** Component interaction testing, database operations, file system operations
- **Location:** tests/integration/
- **Test Infrastructure:**
  - **Database:** In-memory SQLite for fast testing
  - **File System:** Temporary directories with cleanup
  - **Agent System:** Mock agent responses for predictable testing

## Security

### Input Validation
- **Validation Library:** Built-in Python validation with custom validators
- **Validation Location:** All MCP tool entry points and workflow state transitions
- **Required Rules:**
  - All file paths must be validated and sanitized
  - Workflow state inputs must be validated against schemas
  - Agent context data must be sanitized before processing

### Secrets Management
- **Development:** .env file with clear documentation
- **Production:** Environment variables only
- **Code Requirements:**
  - NEVER hardcode API keys or sensitive data
  - Access secrets via os.getenv() with defaults
  - No secrets in logs or error messages

### Data Protection
- **Encryption at Rest:** Not required for local development
- **Encryption in Transit:** HTTPS for external API calls only
- **PII Handling:** No PII data expected in this system
- **Logging Restrictions:** Never log file contents, API keys, or user data

## Next Steps

After completing this architecture document:

1. **Begin Implementation:** Start with Epic 1 - Core Automation Engine
2. **Set up Development Environment:** Initialize new workflow/ directory structure
3. **Database Setup:** Create SQLite schema and connection management
4. **Start Story Development:** Use existing BMAD workflow to implement the first story

The architecture is now complete and ready for implementation using the BMAD automated workflow system we're building!
