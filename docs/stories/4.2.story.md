# Story 4.2: 错误恢复和重试机制

## Status
Approved

## Story
**As a** developer,
**I want** the system to handle errors gracefully and provide recovery options,
**so that** workflow interruptions don't require starting over.

## Acceptance Criteria
1. 系统能够检测和分类不同类型的错误
2. 提供自动重试机制，适用于临时性错误
3. 对于严重错误，提供详细的错误报告和恢复建议
4. 支持从任意中断点恢复工作流程
5. 维护完整的错误日志和调试信息

## Tasks / Subtasks
- [ ] Task 1: 实现错误检测和分类系统 (AC: 1)
  - [ ] 在 workflow/error_handler.py 中创建 ErrorClassifier 类
  - [ ] 定义错误类型分类体系（临时性、永久性、用户错误、系统错误）
  - [ ] 实现错误严重程度评估算法
  - [ ] 创建错误模式识别和匹配机制
- [ ] Task 2: 实现自动重试机制 (AC: 2)
  - [ ] 创建 RetryManager 类，管理重试策略
  - [ ] 实现指数退避重试算法
  - [ ] 添加重试次数限制和超时控制
  - [ ] 实现重试条件判断和智能重试
- [ ] Task 3: 创建错误报告和恢复建议系统 (AC: 3)
  - [ ] 实现详细错误报告生成器
  - [ ] 创建恢复建议生成算法
  - [ ] 实现错误上下文收集和分析
  - [ ] 添加用户友好的错误解释和解决方案
- [ ] Task 4: 实现工作流程恢复机制 (AC: 4)
  - [ ] 扩展检查点系统，支持错误恢复
  - [ ] 实现状态一致性验证和修复
  - [ ] 创建增量恢复和回滚机制
  - [ ] 添加恢复点选择和验证功能
- [ ] Task 5: 建立完整的错误日志系统 (AC: 5)
  - [ ] 实现结构化错误日志记录
  - [ ] 创建调试信息收集和存储
  - [ ] 实现日志轮转和清理机制
  - [ ] 添加日志查询和分析工具
- [ ] Task 6: 集成错误处理到工作流程引擎 (AC: 1-5)
  - [ ] 将错误处理集成到 WorkflowExecutor
  - [ ] 实现错误处理的 MCP 工具接口
  - [ ] 添加错误监控和告警机制
  - [ ] 创建错误处理的性能监控

## Dev Notes

### Previous Story Insights
基于故事 4.1 的一键启动工作流程：
- 需要与 WorkflowExecutor 紧密集成，提供无缝的错误处理
- 利用检查点机制实现精确的恢复功能
- 确保错误处理不影响正常工作流程的性能

### Data Models
**ErrorRecord** [需要新定义]：
```python
@dataclass
class ErrorRecord:
    error_id: str
    error_type: str             # temporary, permanent, user_error, system_error
    severity: str               # low, medium, high, critical
    phase: str                  # 发生错误的工作流程阶段
    component: str              # 发生错误的组件
    error_message: str          # 错误消息
    stack_trace: str            # 堆栈跟踪
    context: Dict[str, Any]     # 错误上下文信息
    retry_count: int            # 重试次数
    recovery_suggestions: List[str]  # 恢复建议
    occurred_at: datetime
    resolved_at: Optional[datetime]
    resolution_method: Optional[str]
```

**RetryPolicy** [需要新定义]：
```python
@dataclass
class RetryPolicy:
    max_retries: int            # 最大重试次数
    base_delay: float           # 基础延迟时间（秒）
    max_delay: float            # 最大延迟时间（秒）
    backoff_multiplier: float   # 退避乘数
    retry_conditions: List[str] # 重试条件
    timeout: Optional[float]    # 超时时间
```

### Component Specifications
**ErrorHandler** [需要新实现]：
- 核心接口：
  - `classify_error(exception: Exception, context: Dict) -> ErrorRecord`
  - `should_retry(error_record: ErrorRecord) -> bool`
  - `execute_with_retry(func: Callable, retry_policy: RetryPolicy) -> Any`
  - `generate_recovery_suggestions(error_record: ErrorRecord) -> List[str]`

### File Locations
基于项目结构 [Source: architecture/source-tree.md]：
- 主要实现：`workflow/error_handler.py`（新建）
- 集成点：`workflow/workflow_executor.py`（扩展）
- 数据模型扩展：`workflow/models.py`
- 日志配置：`workflow/logging_config.py`（新建）

### Error Classification System
**错误类型分类**：
1. **临时性错误**：
   - 网络连接错误
   - 文件系统临时不可用
   - 内存不足（可释放）
   - 外部服务暂时不可用

2. **永久性错误**：
   - 文件不存在
   - 权限不足
   - 配置错误
   - 代码逻辑错误

3. **用户错误**：
   - 无效输入参数
   - 缺少必需文件
   - 配置文件格式错误
   - 用户取消操作

4. **系统错误**：
   - 内存溢出
   - 磁盘空间不足
   - 系统资源耗尽
   - 依赖服务故障

### Retry Strategy Design
**指数退避算法**：
```python
delay = min(base_delay * (backoff_multiplier ** retry_count), max_delay)
```

**重试条件判断**：
- 错误类型为临时性错误
- 重试次数未超过限制
- 总执行时间未超过超时限制
- 错误模式匹配重试条件

**智能重试机制**：
- 基于错误历史调整重试策略
- 动态调整重试间隔和次数
- 考虑系统负载和资源状况
- 实现熔断机制，避免级联故障

### Recovery Suggestion System
**恢复建议生成规则**：
- 基于错误类型提供标准建议
- 分析错误上下文生成定制建议
- 参考历史解决方案
- 提供分步骤的恢复指导

**常见恢复建议**：
- 文件权限错误：检查文件权限设置
- 网络错误：检查网络连接和防火墙设置
- 配置错误：验证配置文件格式和内容
- 资源不足：清理临时文件或增加资源

### Checkpoint Integration
**错误恢复检查点**：
- 每个主要操作前保存检查点
- 错误发生时自动保存错误状态
- 恢复时验证检查点完整性
- 支持多级检查点回滚

**状态一致性验证**：
- 验证文件系统状态
- 检查数据库状态一致性
- 验证工作流程状态完整性
- 修复不一致的状态

### Logging and Debugging System
**结构化日志格式**：
```json
{
  "timestamp": "2025-01-12T10:30:00Z",
  "level": "ERROR",
  "component": "document_processor",
  "phase": "document_sharding",
  "error_id": "err_12345",
  "message": "Failed to shard document",
  "context": {
    "file_path": "docs/prd.md",
    "operation": "shard_documents"
  },
  "stack_trace": "...",
  "recovery_attempted": true
}
```

**调试信息收集**：
- 系统环境信息
- 工作流程状态快照
- 相关文件状态
- 内存和资源使用情况
- 用户操作历史

### Performance Considerations
- 错误处理不应显著影响正常执行性能
- 使用异步日志记录，避免阻塞主流程
- 实现错误处理的缓存机制
- 优化错误分类算法的执行效率

### Integration with Monitoring
**错误监控指标**：
- 错误发生频率和类型分布
- 重试成功率和平均重试次数
- 恢复时间和成功率
- 系统可用性和稳定性指标

**告警机制**：
- 关键错误的实时告警
- 错误频率异常的告警
- 系统资源不足的告警
- 恢复失败的告警

### Technical Constraints
- Python 3.8+ 与严格的类型提示 [Source: architecture/coding-standards.md#core-standards]
- 使用 Python logging 模块，禁止 print() 语句 [Source: architecture/coding-standards.md#critical-rules]
- 所有文件操作必须包含适当的异常处理 [Source: architecture/coding-standards.md#critical-rules]
- 错误处理不得破坏工作流程状态的原子性

### Testing
**测试要求** [Source: architecture/test-strategy-and-standards.md]
- 框架：pytest 7.4+
- 位置：tests/unit/workflow/ 和 tests/integration/
- 覆盖率：新工作流程组件需要 90% 覆盖率
- 模拟：使用 unittest.mock 模拟各种错误场景
- **特殊测试场景**：
  - 各种错误类型的分类准确性
  - 重试机制的正确性和性能
  - 恢复建议的有效性
  - 检查点恢复的完整性
  - 并发错误处理的正确性

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-12 | 1.0 | Initial story creation | SM Agent |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled by dev agent*

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
*To be filled by dev agent*

### File List
*To be filled by dev agent*

## QA Results
### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

错误处理相关核心构件已存在且集成到执行器（分片路径）中：`ErrorClassifier`、`RetryManager`、`RetryPolicy`、结构化错误记录与指数退避重试均可用。缺失检查点/恢复的落地流程与恢复建议生成功能，MCP 层未暴露错误处理与恢复控制工具。

### Refactoring Performed

本次评审未进行代码重构。

### Compliance Check

- Coding Standards: ✓ 基本符合
- Project Structure: ✓ 模块划分清晰（error_handler/models/executor）
- Testing Strategy: △ 有E2E基础测试；缺针对 ErrorClassifier/RetryManager 的覆盖与恢复流程测试
- All ACs Met: ✗ AC3（恢复建议）、AC4（从中断点恢复）未满足；AC5 部分满足

### Improvements Checklist

- [ ] `workflow/workflow_executor.py`：实现最小检查点保存/恢复（JSON状态文件），并在关键操作前后写入检查点
- [ ] `workflow/error_handler.py`：新增 `generate_recovery_suggestions(error_record)` 基础实现（规则映射+上下文）
- [ ] `bmad_agent_mcp.py`：新增 `pause_workflow`、`resume_workflow`、`restart_workflow`、`recover_from_checkpoint` MCP 工具（薄封装 `workflow_executor`/状态文件）
- [ ] 测试：
  - 单测：RetryManager 指数退避/超时；ErrorClassifier 分类覆盖（temporary/permanent/user/system）
  - 集成：模拟分片失败→重试成功；写入检查点后从中断点恢复继续执行
- [ ] 结构化日志：补充日志轮转与最小查询接口（可先用文件切割策略）

### Security Review

结构化日志中包含上下文信息，后续注意敏感信息过滤；从检查点恢复需要校验状态一致性及输入合法性。

### Performance Considerations

重试策略已使用指数退避；加入检查点与恢复后需关注I/O频率与并发访问安全。

### Final Status

✗ Changes Required - 待完成检查点/恢复与恢复建议、测试覆盖与控制工具后复审
