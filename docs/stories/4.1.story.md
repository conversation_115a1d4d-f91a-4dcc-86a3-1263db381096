# Story 4.1: 一键启动工作流程

## Status
Approved

## Story
**As a** developer,
**I want** to start the complete Greenfield development process with a single command,
**so that** I can focus on high-level decisions rather than process management.

## Acceptance Criteria
1. 提供单一的 MCP 工具命令启动完整工作流程
2. 自动执行文档分片、Epic 创建、Story 生成的完整流程
3. 在每个关键节点提供用户确认和干预选项
4. 提供详细的进度报告和状态更新
5. 支持工作流程的暂停、恢复和重启功能

## Tasks / Subtasks
- [ ] Task 1: 实现一键启动工作流程引擎 (AC: 1, 2)
  - [ ] 在 workflow/state_engine.py 中创建 start_greenfield_workflow 方法
  - [ ] 实现完整工作流程的自动化编排
  - [ ] 集成文档分片、Epic 创建、Story 生成的完整流程
  - [ ] 实现工作流程状态的持续跟踪和更新
- [ ] Task 2: 创建用户交互和确认机制 (AC: 3)
  - [ ] 实现关键节点的用户确认提示
  - [ ] 创建交互式进度显示界面
  - [ ] 添加用户干预选项（跳过、重试、自定义）
  - [ ] 实现用户决策的记录和应用
- [ ] Task 3: 实现进度报告和状态更新系统 (AC: 4)
  - [ ] 创建实时进度跟踪机制
  - [ ] 实现详细的状态报告生成
  - [ ] 添加工作流程可视化显示
  - [ ] 实现进度通知和更新推送
- [ ] Task 4: 实现工作流程控制功能 (AC: 5)
  - [ ] 创建工作流程暂停和恢复机制
  - [ ] 实现工作流程重启功能
  - [ ] 添加检查点保存和恢复
  - [ ] 实现工作流程状态的持久化
- [ ] Task 5: 集成所有自动化组件 (AC: 2)
  - [ ] 集成 Document Processor 的分片功能
  - [ ] 集成 Epic 创建和 Story 生成功能
  - [ ] 集成 Agent Orchestrator 的智能体协调
  - [ ] 集成 Code Generator 的代码生成功能
- [ ] Task 6: 创建 MCP 工具接口 (AC: 1)
  - [ ] 在 bmad_agent_mcp.py 中添加 start_greenfield_workflow 工具
  - [ ] 实现工具参数验证和配置
  - [ ] 添加工作流程状态查询工具
  - [ ] 实现工作流程控制工具（暂停、恢复、重启）

## Dev Notes

### Previous Story Insights
基于前面所有 Epic 的实施：
- 需要集成 Epic 1 的核心自动化引擎（状态检测、文档分片、Epic 创建）
- 需要集成 Epic 2 的智能体协作系统（任务传递、上下文加载）
- 需要集成 Epic 3 的真实任务执行（代码生成、测试生成）
- 确保所有组件的无缝协作和状态同步

### Data Models
**WorkflowExecution** [需要新定义]：
```python
@dataclass
class WorkflowExecution:
    execution_id: str
    workflow_type: str          # greenfield, brownfield
    current_phase: str          # document_processing, epic_creation, story_development, implementation
    total_phases: int
    completed_phases: int
    phase_progress: Dict[str, float]  # 每个阶段的进度百分比
    user_confirmations: List[Dict]    # 用户确认记录
    checkpoints: List[Dict]           # 检查点记录
    status: str                 # running, paused, completed, failed
    started_at: datetime
    estimated_completion: Optional[datetime]
    actual_completion: Optional[datetime]
```

**UserInteraction** [需要新定义]：
```python
@dataclass
class UserInteraction:
    interaction_id: str
    phase: str
    message: str
    options: List[str]
    user_choice: Optional[str]
    timestamp: datetime
    auto_proceed: bool          # 是否自动继续
```

### Component Specifications
**Workflow State Engine** [Source: architecture/components.md#workflow-state-engine]
- 核心接口：
  - `start_workflow(project_path: str) -> WorkflowState`
  - `advance_workflow(workflow_id: str) -> WorkflowState`
  - `get_workflow_status(workflow_id: str) -> WorkflowState`
  - `recover_workflow(workflow_id: str, checkpoint: str) -> WorkflowState`
- 依赖：SQLite database, Document Processor, Agent Orchestrator
- 技术栈：Python asyncio, SQLite, dataclasses for state management

### File Locations
基于项目结构 [Source: architecture/source-tree.md]：
- 主要实现：`workflow/state_engine.py`（扩展）
- 新增模块：`workflow/workflow_executor.py`
- 数据模型扩展：`workflow/models.py`
- MCP 集成：`bmad_agent_mcp.py`

### Core Workflow Integration
基于核心工作流程 [Source: architecture/core-workflows.md]：
```
用户启动 → 工作流程引擎 → 文档处理器 → Epic 创建 → Story 循环（SM → Dev → QA）→ 完成
```

**完整工作流程阶段**：
1. **文档处理阶段**：分片 PRD 和 Architecture 文档
2. **Epic 创建阶段**：从分片内容创建 Epic 文件
3. **Story 开发阶段**：为每个 Epic 生成 Story 文件
4. **实施阶段**：智能体协作实施每个 Story
5. **验证阶段**：QA 审查和最终验证

### User Interaction Design
**关键确认节点**：
- 文档分片完成后：确认分片结果是否满意
- Epic 创建完成后：确认 Epic 结构和优先级
- Story 生成完成后：确认 Story 详细程度
- 每个 Story 实施前：确认是否开始实施
- QA 审查后：确认是否接受修改建议

**交互选项**：
- 继续：按计划继续执行
- 暂停：暂停工作流程，稍后恢复
- 重试：重新执行当前阶段
- 跳过：跳过当前阶段（如果允许）
- 自定义：提供自定义参数或配置

### Progress Reporting System
**进度指标**：
- 总体进度：完成阶段数 / 总阶段数
- 阶段进度：当前阶段内的具体进度
- 时间估算：基于历史数据的完成时间预测
- 资源使用：CPU、内存、磁盘使用情况

**状态报告内容**：
- 当前执行阶段和进度
- 已完成的工作项目列表
- 下一步计划和预期时间
- 遇到的问题和解决方案
- 用户干预记录

### Checkpoint and Recovery System
**检查点策略**：
- 每个主要阶段完成后自动保存检查点
- 用户手动暂停时保存检查点
- 错误发生前保存恢复点
- 关键用户决策后保存状态

**恢复机制**：
- 从最近检查点恢复执行
- 选择性恢复到特定阶段
- 状态一致性验证和修复
- 增量恢复，避免重复工作

### Technical Constraints
- Python 3.8+ 与严格的类型提示 [Source: architecture/coding-standards.md#core-standards]
- 使用 Python logging 模块，禁止 print() 语句 [Source: architecture/coding-standards.md#critical-rules]
- 工作流程状态变更必须是原子性的并记录日志 [Source: architecture/coding-standards.md#critical-rules]
- 新 MCP 工具不得破坏现有工具接口 [Source: architecture/coding-standards.md#critical-rules]

### Performance Considerations
- 使用 Python asyncio 实现非阻塞的工作流程执行
- 实现并行处理，在可能的情况下同时执行多个任务
- 优化状态持久化，减少数据库写入频率
- 实现智能缓存，避免重复计算和文件读取

### Error Handling Strategy
- 区分临时性错误和永久性错误
- 实现自动重试机制，适用于网络和文件系统错误
- 提供详细的错误上下文和恢复建议
- 维护完整的错误日志和调试信息

### Testing
**测试要求** [Source: architecture/test-strategy-and-standards.md]
- 框架：pytest 7.4+
- 位置：tests/unit/workflow/ 和 tests/integration/
- 覆盖率：新工作流程组件需要 90% 覆盖率
- 模拟：使用 unittest.mock 模拟用户交互和外部依赖
- **特殊测试场景**：
  - 完整工作流程的端到端测试
  - 用户交互和确认机制测试
  - 检查点保存和恢复功能测试
  - 并发执行和状态同步测试
  - 错误处理和恢复机制测试

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-12 | 1.0 | Initial story creation | SM Agent |

## Dev Agent Record
*This section will be populated by the development agent during implementation*

### Agent Model Used
*To be filled by dev agent*

### Debug Log References
*To be filled by dev agent*

### Completion Notes List
*To be filled by dev agent*

### File List
*To be filled by dev agent*

## QA Results
### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

整体实现为轻量 MVP，已打通入口与状态扫描/报告，但未形成“分片→Epic→Story→实施/审查”的完整自动化链路。代码结构清晰，职责划分合理；缺少用户交互、流程控制与持久化等关键能力。

### Refactoring Performed

本次评审未进行代码级重构（仅评估与定位差距）。

### Compliance Check

- Coding Standards: ✓ 基本符合
- Project Structure: ✓ 符合既定目录与模块划分
- Testing Strategy: △ 存在基础单测与一个E2E扫描测试，但未覆盖本故事核心链路
- All ACs Met: ✗ AC2、AC3、AC5 未满足

### Improvements Checklist

- [x] 在 [`workflow/workflow_executor.py`](workflow/workflow_executor.py:1) 中补齐从 PRD 分片到自动创建 Epics 与 Stories 的最小实现（直写 `docs/epics/`、`docs/stories/`）
- [x] 在执行器中加入关键节点交互（确认/跳过/重试）参数与最小回调机制，落地 `UserInteraction`（`start_workflow_executor` 支持 interaction_callback）
- [x] 增加工作流控制接口：暂停、恢复、重启（已添加 pause_workflow_execution / resume_workflow_execution / restart_workflow_execution，集成 `workflow/state_engine.py` 持久化）
- [ ] 扩展进度与状态：增加实时/增量更新与可视化/推送（基础可先返回结构化报告）
- [ ] 集成 `Agent Orchestrator` 与 `Code Generator` 至该流程形成闭环
- [ ] 新增端到端集成测试：启动→分片→最小 Epic/Story 生成→状态报告（覆盖 AC1/2/4）
- [ ] 强化 MCP 启动工具参数校验与错误处理
- [x] 引入轻量持久化（例如JSON/SQLite）记录执行进度与检查点（已实现 save/load/checkpoint 接口于 `workflow/state_engine.py`）

### Security Review

未发现明显安全风险；后续在持久化与外部调用处需加入输入校验与错误策略。

### Performance Considerations

当前为同步轻量实现，性能风险较低；后续并发/持久化加入后关注 I/O 与并发安全。

### Final Status

✗ Changes Required - 待完成上述未满足项与测试覆盖后再复审

### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

- 一键工作流 MVP 已落地：`workflow/state_engine.py`（扫描/阶段判定/状态报告/检查点持久化）、`workflow/workflow_executor.py`（分片→最小 Epic/Story 生成→交互回调→持久化、暂停/恢复/重启）。
- MCP 侧存在端到端起停与状态报告能力（参考 `tests/integration/test_e2e_workflow.py`）。
- 交互回调 `interaction_callback` 与 `.workflow_state` 持久化已验证（`tests/unit/workflow/test_workflow_executor_interaction.py`）。

不足：
- 未形成“分片→Epic→Story→实施（Orchestrator/CodeGen）→QA”的完整闭环，仅最小生成。
- `workflow/workflow_executor.py` 末尾存在疑似误置的顶层代码块（引用 `summary/scan` 未定义），建议删除或并入函数内。

### Compliance Check

- Coding Standards: △（总体合规；建议补齐关键公开接口类型标注）
- Project Structure: ✓（模块划分与落点基本符合）
- Testing Strategy: △（已含单测与E2E基础；尚缺完整闭环/暂停恢复链路覆盖）
- All ACs Met: △
  - AC1 单一命令启动：✓（MCP 已具备）
  - AC2 自动执行完整流程：△（分片与最小生成具备，未整合 Orchestrator/CodeGen）
  - AC3 关键节点交互：✓（回调与持久化）
  - AC4 进度报告/状态更新：△（`generate_status_report` 可用；未统一纳入执行 Summary）
  - AC5 暂停/恢复/重启：✓（提供最小实现与持久化）

### Improvements Checklist

- [ ] 修正 `workflow/workflow_executor.py` 末尾遗留的顶层代码块（引用未定义变量）
- [ ] 在 `start_workflow_executor` 的返回中统一纳入 `status_report`（调用 `state_engine.generate_status_report`）
- [ ] 打通 Orchestrator/CodeGenerator，形成闭环流（最小故事驱动）
- [ ] 扩充 E2E：启动→分片→Epic/Story→实现触发占位→状态报告与检查点验证
- [ ] MCP 工具增加暂停/恢复/重启/状态查询的参数校验与错误处理

### Security Review

- 以文件持久化为主，风险较低；建议对用户输入与文件路径做严格校验。

### Performance Considerations

- 现为同步MVP；后续并发/长流程需关注并发与I/O。

### Final Status

✗ Changes Required - 修正顶层代码、统一状态报告并打通闭环后再复审
