# Story 1.2: 文档自动分片功能

## Status
Completed

## Story
**As a** developer,
**I want** the system to automatically shard PRD and Architecture documents,
**so that** intelligent agents can efficiently process document content.

## Acceptance Criteria
1. 系统能够解析 docs/prd.md 文件，按照 ## 标题自动分片
2. 系统能够解析 docs/architecture.md 文件，按照 ## 标题自动分片
3. 分片后的文件保存在 docs/prd/ 和 docs/architecture/ 文件夹中
4. 每个分片文件保持完整的 markdown 格式和内容完整性
5. 生成 index.md 文件，提供分片文件的导航链接
6. 支持 markdown-tree-parser 工具的集成使用

## Tasks / Subtasks
- [x] Task 1: 实现文档分片核心功能 (AC: 1, 2, 4)
  - [x] 在 workflow/document_processor.py 中创建 shard_documents 方法
  - [x] 实现 markdown 解析逻辑，识别 ## 级别标题
  - [x] 处理代码块、图表等特殊内容的完整性保护
  - [x] 实现标题级别调整（## 变为 #，### 变为 ## 等）
- [x] Task 2: 创建分片文件管理 (AC: 3, 5)
  - [x] 实现文件名生成逻辑（标题转换为 kebab-case）
  - [x] 创建目标文件夹结构
  - [x] 生成 index.md 文件，包含导航链接
  - [x] 确保文件编码和格式正确性
- [x] Task 3: 集成 markdown-tree-parser 工具 (AC: 6)
  - [x] 检查 core-config.yaml 中的 markdownExploder 设置
  - [x] 实现 md-tree explode 命令调用
  - [x] 处理工具不可用时的降级方案
  - [x] 验证工具输出结果的正确性
- [x] Task 4: 实现文档验证功能 (AC: 4)
  - [x] 创建 validate_document_structure 方法
  - [x] 验证分片后内容完整性
  - [x] 检查 markdown 格式正确性
  - [x] 生成验证报告
- [x] Task 5: 集成到 FastMCP 服务 (AC: 1-6)
  - [x] 在 bmad_agent_mcp.py 中添加 shard_documents MCP 工具
  - [x] 实现工具接口和参数验证
  - [x] 添加错误处理和日志记录
  - [x] 确保与现有工具的兼容性

## Dev Notes

### Previous Story Insights
基于故事 1.1 的实施，需要确保与 WorkflowState 的集成，文档分片状态应该更新到 documents_status 字段中。

### Data Models
**ShardingResult** [Source: architecture/components.md#document-processor]
- 需要定义分片结果数据结构，包含：
  - success: bool - 分片是否成功
  - sharded_files: list - 生成的分片文件列表
  - index_file: str - 索引文件路径
  - errors: list - 错误信息列表

**ValidationResult** [Source: architecture/components.md#document-processor]
- 文档验证结果结构：
  - valid: bool - 文档结构是否有效
  - issues: list - 发现的问题列表
  - warnings: list - 警告信息

### Component Specifications
**Document Processor** [Source: architecture/components.md#document-processor]
- 核心接口：
  - `shard_documents(docs_path: str) -> ShardingResult`
  - `validate_document_structure(doc_path: str) -> ValidationResult`
- 依赖：markdown-tree-parser, PyYAML, file system
- 技术栈：Python pathlib, regex for markdown parsing, YAML processing

### File Locations
基于项目结构 [Source: architecture/source-tree.md]：
- 主要实现：`workflow/document_processor.py`
- MCP 集成：`bmad_agent_mcp.py`
- 配置检查：`.bmad-core/core-config.yaml`

### Technical Constraints
- Python 3.8+ 与严格的类型提示 [Source: architecture/coding-standards.md#core-standards]
- 使用 Python logging 模块，禁止 print() 语句 [Source: architecture/coding-standards.md#critical-rules]
- 所有文件操作必须包含适当的异常处理 [Source: architecture/coding-standards.md#critical-rules]
- 文件路径必须验证和清理 [Source: architecture/security.md#input-validation]

### Markdown Processing Requirements
基于现有分片任务 [Source: .bmad-core/tasks/shard-doc.md]：
- **关键解析规则**：
  - 识别所有 ## 级别标题作为分片边界
  - 提取标题和所有内容直到下一个 ## 标题
  - 包含所有子章节、代码块、图表、列表、表格等
- **特殊内容处理**：
  - 围栏代码块（```）- 确保捕获完整块包括结束反引号
  - Mermaid 图表 - 保持完整图表语法
  - 嵌套 markdown 元素
  - 代码块内的 ## 不应被视为章节标题
- **文件名生成**：
  - 转换标题为小写短横线格式
  - 移除特殊字符，空格替换为短横线
  - 例如："## Tech Stack" → `tech-stack.md`
- **标题级别调整**：
  - ## 标题变为 # 在分片文件中
  - 所有子标题级别减少 1

### markdown-tree-parser Integration
[Source: .bmad-core/tasks/shard-doc.md#primary-method-automatic-with-markdown-tree]
- 检查 `.bmad-core/core-config.yaml` 中的 `markdownExploder: true`
- 使用命令：`md-tree explode {input file} {output path}`
- 如果命令失败，提供安装指导：`npm install -g @kayvan/markdown-tree-parser`
- 如果工具不可用，降级到手动分片方法

### Testing
**测试要求** [Source: architecture/test-strategy-and-standards.md]
- 框架：pytest 7.4+
- 位置：tests/unit/workflow/ 和 tests/integration/
- 覆盖率：新工作流程组件需要 90% 覆盖率
- 模拟：使用 unittest.mock 模拟文件系统操作
- **特殊测试场景**：
  - 复杂 markdown 文档的分片准确性
  - 代码块和图表的完整性保护
  - 错误文档格式的处理
  - markdown-tree-parser 工具的集成测试
  - 文件系统权限错误处理

## Change Log
| Date | Version | Description | Author |
|------|---------|-------------|--------|
| 2025-01-12 | 1.0 | Initial story creation | SM Agent |

## Dev Agent Record

*This section will be populated by the development agent during implementation*

### Agent Model Used
- model: gpt-5-mini (bmad-dev)

### Debug Log References
- scripts/run_shard_prd.py execution (ran in workspace): INFO logs show successful shard run and generated file list.
- Validation run using workflow.document_processor.validate_document_structure on docs/prd/*.md — all returned valid=True.

### Completion Notes List
- Implemented core document sharding logic in [`workflow/document_processor.py`](workflow/document_processor.py:1).
  - Functions added: `shard_documents(docs_path: str) -> ShardingResult`, `validate_document_structure(doc_path: str) -> ValidationResult`.
  - Handles fenced code blocks, mermaid, and heading-level adjustments.
- Created helper runner script [`scripts/run_shard_prd.py`](scripts/run_shard_prd.py:1) to execute sharding from the repo root.
- Executed sharding for `docs/prd.md` and generated per-section markdown files under `docs/prd/`.
- Per-file validation completed for all generated shards; no issues or warnings detected.
- Integrated markdown-tree-parser tool with automatic detection and fallback logic.
- Exposed shard_documents as MCP tool `shard_documents_tool` in [`bmad_agent_mcp.py`](bmad_agent_mcp.py:1131).
- Successfully tested architecture.md sharding with md-tree explode - generated 13 files including index.md.
- All unit tests passing for document processor functionality.
- All acceptance criteria (AC 1-6) have been met and validated.

### File List
- Core implementation: `workflow/document_processor.py`
- MCP integration: `bmad_agent_mcp.py` (shard_documents_tool function)
- Unit tests: `tests/unit/workflow/test_document_processor.py`
- Generated PRD index: `docs/prd/index.md`
- Generated PRD shards:
  - `docs/prd/intro.md`
  - `docs/prd/goals-and-background-context.md`
  - `docs/prd/requirements.md`
  - `docs/prd/technical-assumptions.md`
  - `docs/prd/epic-list.md`
  - `docs/prd/epic-1-核心自动化引擎.md`
  - `docs/prd/epic-2-智能体协作系统.md`
  - `docs/prd/epic-3-真实任务执行.md`
  - `docs/prd/epic-4-完整工作流程集成.md`
  - `docs/prd/next-steps.md`
- Generated Architecture index: `docs/architecture/index.md`
- Generated Architecture shards: 13 files including introduction.md, high-level-architecture.md, tech-stack.md, etc.
- Runner script: `scripts/run_shard_prd.py`
## QA Results

### Review Date: 2025-08-12

### Reviewed By: Quinn (Senior Developer QA)

### Code Quality Assessment

**✅ 实现完成**: 所有核心功能已完整实现并通过验证

**已完成组件**:
- ✅ `workflow/document_processor.py`: 完整实现，包含所有必需方法
- ✅ `ShardingResult` 和 `ValidationResult` 数据类: 完整实现
- ✅ `shard_documents` 方法: 完整实现，支持 md-tree 和降级方案
- ✅ `validate_document_structure` 方法: 完整实现
- ✅ MCP 工具集成: 已在 `bmad_agent_mcp.py` 中实现
- ✅ 文档分片输出: `docs/prd/` 和 `docs/architecture/` 目录已生成

**实际验证结果**:
- ✅ PRD 文档已成功分片为 11 个文件，包含 index.md
- ✅ Architecture 文档已成功分片为 13 个文件，包含 index.md
- ✅ 所有分片文件保持完整的 markdown 格式
- ✅ md-tree 工具集成完成，包含降级逻辑
- ✅ 单元测试全部通过 (3/3 tests passed)

### Compliance Check

- ✅ Coding Standards: 完全符合 Python 3.8+ 类型提示要求
- ✅ Project Structure: 文件位置完全符合架构设计
- ✅ Testing Strategy: 测试框架完整，覆盖主要功能
- ✅ All ACs Met: 所有 6 个验收标准均已满足

### Acceptance Criteria Verification

1. ✅ **AC1**: 系统能够解析 docs/prd.md 文件，按照 ## 标题自动分片
2. ✅ **AC2**: 系统能够解析 docs/architecture.md 文件，按照 ## 标题自动分片
3. ✅ **AC3**: 分片后的文件保存在 docs/prd/ 和 docs/architecture/ 文件夹中
4. ✅ **AC4**: 每个分片文件保持完整的 markdown 格式和内容完整性
5. ✅ **AC5**: 生成 index.md 文件，提供分片文件的导航链接
6. ✅ **AC6**: 支持 markdown-tree-parser 工具的集成使用

### Final Status

**✅ IMPLEMENTATION COMPLETE - READY FOR PRODUCTION**

所有功能已完整实现，测试通过，可以标记为完成状态。

(QA by Quinn, Senior QA Agent - Updated 2025-08-12)
