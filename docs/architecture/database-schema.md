# Database Schema

```sql
-- Workflow state tracking
CREATE TABLE workflow_states (
    id TEXT PRIMARY KEY,
    current_stage TEXT NOT NULL,
    project_path TEXT NOT NULL,
    documents_status TEXT, -- JSON
    epic_progress TEXT,    -- <PERSON>SO<PERSON>
    story_progress TEXT,   -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Story development tracking
CREATE TABLE story_states (
    story_id TEXT PRIMARY KEY,
    workflow_id TEXT NOT NULL,
    title TEXT NOT NULL,
    status TEXT NOT NULL,
    assigned_agent TEXT,
    file_path TEXT,
    acceptance_criteria TEXT, -- <PERSON><PERSON><PERSON>
    tasks_completed TEXT,     -- <PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    FOREIGN KEY (workflow_id) REFERENCES workflow_states(id)
);

-- Task execution tracking
CREATE TABLE task_executions (
    id TEXT PRIMARY KEY,
    story_id TEXT NOT NULL,
    agent_id TEXT NOT NULL,
    task_type TEXT NOT NULL,
    status TEXT NOT NULL,
    input_context TEXT,  -- JSON
    output_result TEXT,  -- JSON
    error_message TEXT,
    execution_time REAL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (story_id) REFERENCES story_states(story_id)
);

-- Indexes for performance
CREATE INDEX idx_workflow_stage ON workflow_states(current_stage);
CREATE INDEX idx_story_status ON story_states(status);
CREATE INDEX idx_task_status ON task_executions(status);
```
