# Coding Standards

## Core Standards
- **Languages & Runtimes:** Python 3.8+, strict type hints required
- **Style & Linting:** black (formatting), flake8 (linting), mypy (type checking)
- **Test Organization:** pytest with tests/ directory structure, test files named test_*.py

## Critical Rules
- **Logging Only:** Never use print() statements - use Python logging module exclusively
- **Type Safety:** All functions must have complete type hints including return types
- **Error Handling:** All file operations must include proper exception handling with specific error types
- **State Consistency:** All workflow state changes must be atomic and logged
- **Backward Compatibility:** New MCP tools must not break existing tool interfaces
