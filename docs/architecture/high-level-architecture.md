# High Level Architecture

## Technical Summary

The system follows an enhanced monolithic service architecture built on the FastMCP framework, extending the existing tool-based pattern with intelligent workflow automation capabilities. The core enhancement introduces a workflow state management engine that orchestrates document processing, agent collaboration, and task execution in an automated pipeline. The architecture maintains full backward compatibility while adding sophisticated automation layers that transform manual processes into intelligent, self-managing workflows.

## High Level Overview

**Architectural Style:** Enhanced Monolithic Service with Workflow Automation Engine
**Repository Structure:** Monorepo (existing structure maintained)
**Service Architecture:** Single Python service with modular MCP tool extensions
**Primary Flow:** Document → Workflow Engine → Agent Orchestration → Task Execution → Code Generation

**Key Architectural Decisions:**
1. **Extend vs Rebuild:** Enhance existing FastMCP service rather than rebuild
2. **Workflow Engine:** Central orchestration component for automation
3. **State Management:** SQLite-based local state tracking
4. **Agent Collaboration:** Event-driven communication between agents
5. **Backward Compatibility:** All existing tools and agents remain functional

## High Level Project Diagram

```mermaid
graph TD
    A[docs/ Folder<br/>brief.md, prd.md, architecture.md] --> B[Document Processor]
    B --> C[Workflow State Engine]
    C --> D[Agent Orchestrator]
    D --> E[SM Agent]
    D --> F[Dev Agent]
    D --> G[QA Agent]
    E --> H[Story Generator]
    F --> I[Code Generator]
    G --> J[Code Reviewer]
    H --> K[Task Executor]
    I --> K
    J --> K
    K --> L[File System<br/>Generated Code & Docs]
    C --> M[SQLite State DB]
    N[Cursor IDE] --> O[FastMCP Protocol]
    O --> C
```

## Architectural and Design Patterns

- **Workflow Orchestration Pattern:** Central workflow engine manages state transitions and agent coordination
- **Command Pattern:** MCP tools as discrete commands with consistent interfaces
- **State Machine Pattern:** Workflow progression through defined states with validation
- **Observer Pattern:** Agents subscribe to workflow state changes for automatic activation
- **Template Method Pattern:** Consistent document processing and code generation workflows
- **Repository Pattern:** Abstract data access for state management and file operations
