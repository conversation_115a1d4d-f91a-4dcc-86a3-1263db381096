# BMAD Agent FastMCP Service v2.0 Architecture Document

## Table of Contents

- [BMAD Agent FastMCP Service v2.0 Architecture Document](#table-of-contents)
  - [Introduction](./introduction.md)
    - [Starter Template or Existing Project](./introduction.md#starter-template-or-existing-project)
    - [Change Log](./introduction.md#change-log)
  - [High Level Architecture](./high-level-architecture.md)
    - [Technical Summary](./high-level-architecture.md#technical-summary)
    - [High Level Overview](./high-level-architecture.md#high-level-overview)
    - [High Level Project Diagram](./high-level-architecture.md#high-level-project-diagram)
    - [Architectural and Design Patterns](./high-level-architecture.md#architectural-and-design-patterns)
  - [Tech Stack](./tech-stack.md)
    - [Cloud Infrastructure](./tech-stack.md#cloud-infrastructure)
    - [Technology Stack Table](./tech-stack.md#technology-stack-table)
  - [Data Models](./data-models.md)
    - [WorkflowState](./data-models.md#workflowstate)
    - [StoryState](./data-models.md#storystate)
    - [TaskExecution](./data-models.md#taskexecution)
  - [Components](./components.md)
    - [Workflow State Engine](./components.md#workflow-state-engine)
    - [Document Processor](./components.md#document-processor)
    - [Agent Orchestrator](./components.md#agent-orchestrator)
    - [Story Generator](./components.md#story-generator)
    - [Code Generator](./components.md#code-generator)
    - [Component Diagrams](./components.md#component-diagrams)
  - [Core Workflows](./core-workflows.md)
  - [Database Schema](./database-schema.md)
  - [Source Tree](./source-tree.md)
  - [Coding Standards](./coding-standards.md)
    - [Core Standards](./coding-standards.md#core-standards)
    - [Critical Rules](./coding-standards.md#critical-rules)
  - [Test Strategy and Standards](./test-strategy-and-standards.md)
    - [Testing Philosophy](./test-strategy-and-standards.md#testing-philosophy)
    - [Test Types and Organization](./test-strategy-and-standards.md#test-types-and-organization)
      - [Unit Tests](./test-strategy-and-standards.md#unit-tests)
      - [Integration Tests](./test-strategy-and-standards.md#integration-tests)
  - [Security](./security.md)
    - [Input Validation](./security.md#input-validation)
    - [Secrets Management](./security.md#secrets-management)
    - [Data Protection](./security.md#data-protection)
  - [Next Steps](./next-steps.md)
