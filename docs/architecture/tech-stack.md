# Tech Stack

## Cloud Infrastructure
- **Provider:** Local Development (No cloud dependency)
- **Key Services:** File system storage, local SQLite database
- **Deployment Regions:** Local machine only

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| **Language** | Python | 3.8+ | Primary development language | Existing codebase, excellent AI/ML libraries |
| **Framework** | FastMCP | Latest | MCP service framework | Existing foundation, Cursor IDE integration |
| **Database** | SQLite | 3.40+ | Local state management | Lightweight, no setup required, perfect for local dev |
| **Document Processing** | PyYAML | 6.0+ | YAML configuration parsing | Existing dependency, handles .bmad-core configs |
| **File Operations** | Pathlib | Built-in | File system operations | Python standard library, robust path handling |
| **State Management** | Dataclasses | Built-in | Data structure definitions | Type safety, clean data models |
| **Async Processing** | asyncio | Built-in | Asynchronous workflow execution | Non-blocking agent operations |
| **Testing** | pytest | 7.4+ | Unit and integration testing | Industry standard, excellent plugin ecosystem |
| **Code Quality** | black, flake8 | Latest | Code formatting and linting | Maintain code consistency |
| **Documentation** | markdown-tree-parser | Latest | Document sharding utility | Existing BMAD tool for document processing |
