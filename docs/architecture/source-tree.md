# Source Tree

```
bmad-agent-fastmcp/
├── bmad_agent_mcp.py           # Main FastMCP service (existing)
├── llm_client.py               # LLM client (existing)
├── utils.py                    # Utilities (existing)
├── requirements.txt            # Dependencies (existing)
├── .env                        # Environment config (existing)
│
├── workflow/                   # NEW: Workflow automation components
│   ├── __init__.py
│   ├── state_engine.py         # Workflow State Engine
│   ├── document_processor.py   # Document processing and sharding
│   ├── agent_orchestrator.py   # Agent coordination
│   ├── story_generator.py      # Story creation automation
│   ├── code_generator.py       # Real code generation
│   └── models.py              # Data models for workflow state
│
├── database/                   # NEW: Database management
│   ├── __init__.py
│   ├── schema.sql             # Database schema
│   ├── migrations/            # Schema migrations
│   └── connection.py          # Database connection management
│
├── .bmad-core/                # Existing BMAD configuration
│   ├── agents/                # Agent configurations (existing)
│   ├── workflows/             # Workflow definitions (existing)
│   ├── tasks/                 # Task definitions (existing)
│   └── templates/             # Document templates (existing)
│
├── docs/                      # Project documentation
│   ├── brief.md              # Project brief (NEW)
│   ├── prd.md                # Product requirements (NEW)
│   ├── architecture.md       # This document (NEW)
│   ├── prd/                  # Sharded PRD files (generated)
│   ├── architecture/         # Sharded architecture files (generated)
│   ├── epics/                # Epic files (generated)
│   └── stories/              # Story files (generated)
│
├── tests/                     # Test suite
│   ├── unit/                 # Unit tests
│   ├── integration/          # Integration tests
│   └── fixtures/             # Test data and fixtures
│
└── logs/                     # Application logs (existing)
```
