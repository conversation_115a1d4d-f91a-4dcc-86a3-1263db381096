# Data Models

## WorkflowState
**Purpose:** Track the current state and progress of the automated development workflow

**Key Attributes:**
- workflow_id: str - Unique identifier for the workflow instance
- current_stage: str - Current workflow stage (document_processing, epic_creation, story_development, etc.)
- project_path: str - Path to the project root directory
- documents_status: dict - Status of document processing (sharded, validated, etc.)
- epic_progress: dict - Progress tracking for each epic
- story_progress: dict - Progress tracking for each story
- created_at: datetime - Workflow creation timestamp
- updated_at: datetime - Last update timestamp

**Relationships:**
- One-to-many with StoryState
- One-to-many with TaskExecution

## StoryState
**Purpose:** Track the state and progress of individual story development

**Key Attributes:**
- story_id: str - Epic.Story identifier (e.g., "1.2")
- title: str - Story title
- status: str - Current status (draft, in_progress, review, done)
- assigned_agent: str - Currently responsible agent
- file_path: str - Path to the story markdown file
- acceptance_criteria: list - List of acceptance criteria
- tasks_completed: list - Completed development tasks
- created_at: datetime - Story creation timestamp
- completed_at: datetime - Story completion timestamp

**Relationships:**
- Many-to-one with WorkflowState
- One-to-many with TaskExecution

## TaskExecution
**Purpose:** Track individual task execution within stories

**Key Attributes:**
- task_id: str - Unique task identifier
- story_id: str - Parent story identifier
- agent_id: str - Executing agent identifier
- task_type: str - Type of task (code_generation, testing, review, etc.)
- status: str - Execution status (pending, running, completed, failed)
- input_context: dict - Task input parameters and context
- output_result: dict - Task execution results
- error_message: str - Error details if failed
- execution_time: float - Task execution duration
- created_at: datetime - Task creation timestamp

**Relationships:**
- Many-to-one with StoryState
- Many-to-one with WorkflowState
