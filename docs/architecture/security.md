# Security

## Input Validation
- **Validation Library:** Built-in Python validation with custom validators
- **Validation Location:** All MCP tool entry points and workflow state transitions
- **Required Rules:**
  - All file paths must be validated and sanitized
  - Workflow state inputs must be validated against schemas
  - Agent context data must be sanitized before processing

## Secrets Management
- **Development:** .env file with clear documentation
- **Production:** Environment variables only
- **Code Requirements:**
  - NEVER hardcode API keys or sensitive data
  - Access secrets via os.getenv() with defaults
  - No secrets in logs or error messages

## Data Protection
- **Encryption at Rest:** Not required for local development
- **Encryption in Transit:** HTTPS for external API calls only
- **PII Handling:** No PII data expected in this system
- **Logging Restrictions:** Never log file contents, API keys, or user data
