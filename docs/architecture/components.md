# Components

## Workflow State Engine
**Responsibility:** Central orchestration of the automated development workflow, managing state transitions and coordinating agent activities

**Key Interfaces:**
- `start_workflow(project_path: str) -> WorkflowState`
- `advance_workflow(workflow_id: str) -> WorkflowState`
- `get_workflow_status(workflow_id: str) -> WorkflowState`
- `recover_workflow(workflow_id: str, checkpoint: str) -> WorkflowState`

**Dependencies:** SQLite database, Document Processor, Agent Orchestrator

**Technology Stack:** Python asyncio, SQLite, dataclasses for state management

## Document Processor
**Responsibility:** Automated processing of project documents including sharding, validation, and Epic extraction

**Key Interfaces:**
- `shard_documents(docs_path: str) -> ShardingResult`
- `validate_document_structure(doc_path: str) -> ValidationResult`
- `extract_epics(prd_shards: list) -> list[Epic]`
- `create_epic_files(epics: list) -> list[str]`

**Dependencies:** markdown-tree-parser, PyYAML, file system

**Technology Stack:** Python pathlib, regex for markdown parsing, YAML processing

## Agent Orchestrator
**Responsibility:** Coordinate agent activation, task assignment, and inter-agent communication

**Key Interfaces:**
- `activate_agent(agent_id: str, context: dict) -> AgentSession`
- `assign_task(agent_id: str, task: Task) -> TaskExecution`
- `transfer_context(from_agent: str, to_agent: str, context: dict) -> bool`
- `get_agent_status(agent_id: str) -> AgentStatus`

**Dependencies:** Existing agent configurations, Workflow State Engine

**Technology Stack:** FastMCP agent system, Python asyncio for coordination

## Story Generator
**Responsibility:** Automated creation of detailed story files from Epic definitions and architecture context

**Key Interfaces:**
- `generate_story(epic_id: str, story_number: int) -> Story`
- `enrich_story_context(story: Story, architecture_context: dict) -> Story`
- `validate_story_completeness(story: Story) -> ValidationResult`

**Dependencies:** Document Processor (for architecture context), existing story templates

**Technology Stack:** Python template processing, YAML configuration, markdown generation

## Code Generator
**Responsibility:** Real code generation and file operations based on story requirements

**Key Interfaces:**
- `generate_code(story: Story, task: Task) -> CodeGenerationResult`
- `create_files(file_specs: list) -> list[str]`
- `modify_files(modifications: list) -> ModificationResult`
- `generate_tests(code_files: list) -> list[str]`

**Dependencies:** Story definitions, architecture specifications, coding standards

**Technology Stack:** Python AST manipulation, Jinja2 templates, file system operations

## Component Diagrams

```mermaid
graph TB
    subgraph "Workflow Engine Core"
        WSE[Workflow State Engine]
        DP[Document Processor]
        AO[Agent Orchestrator]
    end

    subgraph "Agent Layer"
        SM[SM Agent]
        DEV[Dev Agent]
        QA[QA Agent]
    end

    subgraph "Execution Layer"
        SG[Story Generator]
        CG[Code Generator]
        CR[Code Reviewer]
    end

    subgraph "Storage Layer"
        FS[File System]
        DB[(SQLite DB)]
    end

    WSE --> DP
    WSE --> AO
    WSE --> DB
    AO --> SM
    AO --> DEV
    AO --> QA
    SM --> SG
    DEV --> CG
    QA --> CR
    SG --> FS
    CG --> FS
    CR --> FS
    DP --> FS
```
