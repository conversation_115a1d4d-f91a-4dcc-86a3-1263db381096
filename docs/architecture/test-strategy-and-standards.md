# Test Strategy and Standards

## Testing Philosophy
- **Approach:** Test-driven development for new components, comprehensive test coverage for workflow automation
- **Coverage Goals:** 90% code coverage for workflow components, 80% for existing code modifications
- **Test Pyramid:** 70% unit tests, 25% integration tests, 5% end-to-end workflow tests

## Test Types and Organization

### Unit Tests
- **Framework:** pytest 7.4+
- **File Convention:** test_*.py in tests/unit/ directory
- **Location:** tests/unit/ mirroring source structure
- **Mocking Library:** unittest.mock (built-in)
- **Coverage Requirement:** 90% for new workflow components

**AI Agent Requirements:**
- Generate tests for all public methods
- Cover edge cases and error conditions
- Follow AAA pattern (Arrange, Act, Assert)
- Mock all external dependencies (file system, database)

### Integration Tests
- **Scope:** Component interaction testing, database operations, file system operations
- **Location:** tests/integration/
- **Test Infrastructure:**
  - **Database:** In-memory SQLite for fast testing
  - **File System:** Temporary directories with cleanup
  - **Agent System:** Mock agent responses for predictable testing
