# Core Workflows

```mermaid
sequenceDiagram
    participant U as User
    participant WSE as Workflow Engine
    participant DP as Document Processor
    participant SM as SM Agent
    participant DEV as Dev Agent
    participant QA as QA Agent
    participant FS as File System

    U->>WSE: start_greenfield_workflow()
    WSE->>DP: shard_documents(docs/)
    DP->>FS: Create sharded files
    DP-->>WSE: Sharding complete

    WSE->>DP: extract_epics(prd_shards)
    DP->>FS: Create epic files
    DP-->>WSE: Epics created

    loop For each Story
        WSE->>SM: generate_next_story()
        SM->>FS: Create story file
        SM-->>WSE: Story ready

        WSE->>DEV: implement_story()
        DEV->>FS: Generate code & tests
        DEV-->>WSE: Implementation complete

        WSE->>QA: review_story()
        QA->>FS: Review & refactor code
        alt QA Approves
            QA-->>WSE: Story approved
        else QA Requests Changes
            QA-->>WSE: Changes needed
            WSE->>DEV: address_feedback()
        end
    end

    WSE-->>U: Workflow complete
```
