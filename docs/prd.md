# BMAD Agent FastMCP Service v2.0 Product Requirements Document (PRD)

## Goals and Background Context

### Goals
- 实现从文档到代码的端到端自动化开发工作流程
- 将手动工作流程操作时间减少 80%（从 45-100 分钟降至 10-20 分钟）
- 提供真实的任务执行能力，替代现有的模拟执行
- 建立智能体间的自动协作机制，减少手动切换
- 创建智能的工作流程状态管理和进度跟踪系统

### Background Context
现有的 BMAD Agent FastMCP Service 提供了完整的智能体生态系统，但缺乏真正的自动化能力。开发者仍需要手动推进工作流程的每个步骤，从文档分片到 Story 创建，每个阶段都需要人工干预。这导致了大量的非生产性时间消耗和用户体验的不连贯。

本 PRD 旨在将现有系统升级为真正的自动化开发工作流程平台，实现从项目文档开始的完整自动化开发生命周期。

### Change Log
| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2024-12-19 | 1.0 | Initial PRD creation | Mary (Business Analyst) |

## Requirements

### Functional

1. **FR1**: 系统能够自动检测 docs/ 文件夹中的 brief.md、prd.md、architecture.md 三个核心文档
2. **FR2**: 系统能够自动执行文档分片，将 prd.md 和 architecture.md 分解为结构化的小文件
3. **FR3**: 系统能够基于分片的 PRD 文档自动识别和创建 Epic 文件
4. **FR4**: 系统能够自动分析当前项目状态，识别下一个需要创建的 Story
5. **FR5**: 系统能够基于 Epic 和架构文档自动生成详细的 Story 文件，包含完整的技术上下文
6. **FR6**: 系统能够自动执行真实的任务，包括代码生成、文件创建和修改
7. **FR7**: 系统能够自动在 SM、Dev、QA 智能体间传递任务和上下文
8. **FR8**: 系统能够自动跟踪工作流程状态，验证前置条件，并推进到下一步骤
9. **FR9**: 系统能够提供一键启动完整 Greenfield 开发流程的功能
10. **FR10**: 系统能够在工作流程出错时提供恢复和重试机制

### Non Functional

1. **NFR1**: 工作流程步骤的响应时间必须小于 5 秒
2. **NFR2**: 系统必须与现有的 FastMCP 架构完全兼容
3. **NFR3**: 自动生成的代码和文档质量评分必须达到 4.0/5.0 以上
4. **NFR4**: 系统必须支持本地运行，无需外部云服务依赖
5. **NFR5**: 工作流程的自动化执行比例必须达到 85% 以上
6. **NFR6**: 系统必须保持现有的双 LLM 模式支持（内置 LLM + DeepSeek API）

## Technical Assumptions

### Repository Structure: Monorepo
继续使用现有的单仓库结构，所有新功能作为现有项目的扩展。

### Service Architecture
保持单体应用架构，专注于本地性能优化。新的自动化功能将作为现有 MCP 工具的扩展实现。

### Testing Requirements
采用单元测试 + 集成测试策略，确保自动化工作流程的可靠性。每个新的 MCP 工具都必须有对应的测试用例。

### Additional Technical Assumptions and Requests
- 必须保持与 Cursor IDE MCP 协议的深度集成
- 新功能必须支持现有的 10 个智能体配置
- 文档分片功能应支持 markdown-tree-parser 工具
- 状态管理使用 SQLite 进行本地存储
- 所有文件操作必须包含完整的错误处理和回滚机制

## Epic List

1. **Epic 1: 核心自动化引擎**: 建立工作流程状态管理、文档分片和基础自动化框架
2. **Epic 2: 智能体协作系统**: 实现 SM、Dev、QA 智能体间的自动协作和任务传递
3. **Epic 3: 真实任务执行**: 将模拟任务执行升级为真实的代码生成和文件操作
4. **Epic 4: 完整工作流程集成**: 整合所有组件，实现端到端的自动化开发流程

## Epic 1: 核心自动化引擎

建立项目的自动化基础设施，包括工作流程状态管理、文档处理和基础的自动化框架，为后续的智能体协作和任务执行奠定基础。

### Story 1.1: 工作流程状态检测系统
As a developer,
I want the system to automatically detect the current workflow stage,
so that I don't need to manually track project progress.

#### Acceptance Criteria
1. 系统能够扫描项目文件结构，识别当前处于哪个开发阶段
2. 系统能够检测 docs/ 文件夹中的核心文档（brief.md, prd.md, architecture.md）
3. 系统能够识别已存在的 Epic 和 Story 文件
4. 系统能够确定下一个需要执行的工作流程步骤
5. 提供清晰的状态报告，显示当前进度和下一步行动

### Story 1.2: 文档自动分片功能
As a developer,
I want the system to automatically shard PRD and Architecture documents,
so that intelligent agents can efficiently process document content.

#### Acceptance Criteria
1. 系统能够解析 docs/prd.md 文件，按照 ## 标题自动分片
2. 系统能够解析 docs/architecture.md 文件，按照 ## 标题自动分片
3. 分片后的文件保存在 docs/prd/ 和 docs/architecture/ 文件夹中
4. 每个分片文件保持完整的 markdown 格式和内容完整性
5. 生成 index.md 文件，提供分片文件的导航链接
6. 支持 markdown-tree-parser 工具的集成使用

### Story 1.3: Epic 自动创建系统
As a product owner,
I want the system to automatically create Epic files from sharded PRD content,
so that development work can be properly organized.

#### Acceptance Criteria
1. 系统能够从 docs/prd/ 分片文件中识别 Epic 定义
2. 系统能够为每个 Epic 创建独立的文件：docs/epics/epic-{N}.md
3. Epic 文件包含完整的目标描述和 Story 列表
4. 系统能够验证 Epic 的逻辑顺序和依赖关系
5. 提供 Epic 创建的状态报告和验证结果

## Epic 2: 智能体协作系统

实现 SM、Dev、QA 智能体之间的自动协作机制，包括任务传递、上下文共享和状态同步，确保工作流程的顺畅进行。

### Story 2.1: 智能体任务传递机制
As a scrum master agent,
I want to automatically pass completed stories to the development agent,
so that the development process can continue without manual intervention.

#### Acceptance Criteria
1. SM 完成 Story 创建后，自动通知 Dev 智能体
2. Dev 完成开发后，自动通知 QA 智能体进行审查
3. QA 完成审查后，根据结果决定返回 Dev 或标记完成
4. 每次任务传递都包含完整的上下文信息
5. 提供任务传递的日志和状态跟踪

### Story 2.2: 上下文自动加载系统
As an intelligent agent,
I want to automatically load relevant document fragments and context,
so that I can work efficiently without manual document searching.

#### Acceptance Criteria
1. 智能体激活时自动加载相关的文档分片
2. 根据当前任务类型智能选择需要的架构文档部分
3. 维护智能体间的共享上下文状态
4. 提供上下文加载的性能优化和缓存机制
5. 支持上下文的增量更新和同步

## Epic 3: 真实任务执行

将现有的模拟任务执行升级为真实的代码生成和文件操作能力，使智能体能够实际完成开发任务。

### Story 3.1: 代码生成引擎
As a development agent,
I want to generate real code files based on story requirements,
so that I can actually implement the required functionality.

#### Acceptance Criteria
1. 系统能够根据 Story 要求生成实际的代码文件
2. 支持多种编程语言和框架的代码生成
3. 生成的代码符合项目的编码标准和架构要求
4. 提供代码质量验证和语法检查
5. 支持代码文件的创建、修改和删除操作

### Story 3.2: 测试自动生成
As a development agent,
I want to automatically generate unit tests for implemented code,
so that code quality and reliability are ensured.

#### Acceptance Criteria
1. 为每个生成的代码文件自动创建对应的测试文件
2. 测试覆盖所有的公共方法和关键逻辑路径
3. 测试符合项目的测试策略和框架要求
4. 提供测试执行和结果验证功能
5. 支持测试的自动运行和持续集成

## Epic 4: 完整工作流程集成

整合所有自动化组件，实现从文档到代码的端到端自动化开发流程，提供完整的用户体验。

### Story 4.1: 一键启动工作流程
As a developer,
I want to start the complete Greenfield development process with a single command,
so that I can focus on high-level decisions rather than process management.

#### Acceptance Criteria
1. 提供单一的 MCP 工具命令启动完整工作流程
2. 自动执行文档分片、Epic 创建、Story 生成的完整流程
3. 在每个关键节点提供用户确认和干预选项
4. 提供详细的进度报告和状态更新
5. 支持工作流程的暂停、恢复和重启功能

### Story 4.2: 错误恢复和重试机制
As a developer,
I want the system to handle errors gracefully and provide recovery options,
so that workflow interruptions don't require starting over.

#### Acceptance Criteria
1. 系统能够检测和分类不同类型的错误
2. 提供自动重试机制，适用于临时性错误
3. 对于严重错误，提供详细的错误报告和恢复建议
4. 支持从任意中断点恢复工作流程
5. 维护完整的错误日志和调试信息

## Next Steps

### UX Expert Prompt
基于此 PRD，请创建前端规范文档，重点关注工作流程状态的可视化展示和用户交互设计。

### Architect Prompt
基于此 PRD 和项目简介，请创建技术架构文档，详细设计自动化工作流程引擎的技术实现方案，包括 MCP 工具扩展、状态管理、智能体协作机制和任务执行引擎的架构设计。
