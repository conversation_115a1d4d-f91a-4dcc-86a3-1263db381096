# Epic 2: 智能体协作系统

实现 SM、Dev、QA 智能体之间的自动协作机制，包括任务传递、上下文共享和状态同步，确保工作流程的顺畅进行。

## Story 2.1: 智能体任务传递机制
As a scrum master agent,
I want to automatically pass completed stories to the development agent,
so that the development process can continue without manual intervention.

### Acceptance Criteria
1. SM 完成 Story 创建后，自动通知 Dev 智能体
2. Dev 完成开发后，自动通知 QA 智能体进行审查
3. QA 完成审查后，根据结果决定返回 Dev 或标记完成
4. 每次任务传递都包含完整的上下文信息
5. 提供任务传递的日志和状态跟踪

## Story 2.2: 上下文自动加载系统
As an intelligent agent,
I want to automatically load relevant document fragments and context,
so that I can work efficiently without manual document searching.

### Acceptance Criteria
1. 智能体激活时自动加载相关的文档分片
2. 根据当前任务类型智能选择需要的架构文档部分
3. 维护智能体间的共享上下文状态
4. 提供上下文加载的性能优化和缓存机制
5. 支持上下文的增量更新和同步
