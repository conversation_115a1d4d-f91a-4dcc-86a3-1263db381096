# Requirements

## Functional

1. **FR1**: 系统能够自动检测 docs/ 文件夹中的 brief.md、prd.md、architecture.md 三个核心文档
2. **FR2**: 系统能够自动执行文档分片，将 prd.md 和 architecture.md 分解为结构化的小文件
3. **FR3**: 系统能够基于分片的 PRD 文档自动识别和创建 Epic 文件
4. **FR4**: 系统能够自动分析当前项目状态，识别下一个需要创建的 Story
5. **FR5**: 系统能够基于 Epic 和架构文档自动生成详细的 Story 文件，包含完整的技术上下文
6. **FR6**: 系统能够自动执行真实的任务，包括代码生成、文件创建和修改
7. **FR7**: 系统能够自动在 SM、Dev、QA 智能体间传递任务和上下文
8. **FR8**: 系统能够自动跟踪工作流程状态，验证前置条件，并推进到下一步骤
9. **FR9**: 系统能够提供一键启动完整 Greenfield 开发流程的功能
10. **FR10**: 系统能够在工作流程出错时提供恢复和重试机制

## Non Functional

1. **NFR1**: 工作流程步骤的响应时间必须小于 5 秒
2. **NFR2**: 系统必须与现有的 FastMCP 架构完全兼容
3. **NFR3**: 自动生成的代码和文档质量评分必须达到 4.0/5.0 以上
4. **NFR4**: 系统必须支持本地运行，无需外部云服务依赖
5. **NFR5**: 工作流程的自动化执行比例必须达到 85% 以上
6. **NFR6**: 系统必须保持现有的双 LLM 模式支持（内置 LLM + DeepSeek API）
