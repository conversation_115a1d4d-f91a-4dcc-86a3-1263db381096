# Epic 1: 核心自动化引擎

建立项目的自动化基础设施，包括工作流程状态管理、文档处理和基础的自动化框架，为后续的智能体协作和任务执行奠定基础。

## Story 1.1: 工作流程状态检测系统
As a developer,
I want the system to automatically detect the current workflow stage,
so that I don't need to manually track project progress.

### Acceptance Criteria
1. 系统能够扫描项目文件结构，识别当前处于哪个开发阶段
2. 系统能够检测 docs/ 文件夹中的核心文档（brief.md, prd.md, architecture.md）
3. 系统能够识别已存在的 Epic 和 Story 文件
4. 系统能够确定下一个需要执行的工作流程步骤
5. 提供清晰的状态报告，显示当前进度和下一步行动

## Story 1.2: 文档自动分片功能
As a developer,
I want the system to automatically shard PRD and Architecture documents,
so that intelligent agents can efficiently process document content.

### Acceptance Criteria
1. 系统能够解析 docs/prd.md 文件，按照 ## 标题自动分片
2. 系统能够解析 docs/architecture.md 文件，按照 ## 标题自动分片
3. 分片后的文件保存在 docs/prd/ 和 docs/architecture/ 文件夹中
4. 每个分片文件保持完整的 markdown 格式和内容完整性
5. 生成 index.md 文件，提供分片文件的导航链接
6. 支持 markdown-tree-parser 工具的集成使用

## Story 1.3: Epic 自动创建系统
As a product owner,
I want the system to automatically create Epic files from sharded PRD content,
so that development work can be properly organized.

### Acceptance Criteria
1. 系统能够从 docs/prd/ 分片文件中识别 Epic 定义
2. 系统能够为每个 Epic 创建独立的文件：docs/epics/epic-{N}.md
3. Epic 文件包含完整的目标描述和 Story 列表
4. 系统能够验证 Epic 的逻辑顺序和依赖关系
5. 提供 Epic 创建的状态报告和验证结果
