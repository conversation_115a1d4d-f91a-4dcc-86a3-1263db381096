# Technical Assumptions

## Repository Structure: Monorepo
继续使用现有的单仓库结构，所有新功能作为现有项目的扩展。

## Service Architecture
保持单体应用架构，专注于本地性能优化。新的自动化功能将作为现有 MCP 工具的扩展实现。

## Testing Requirements
采用单元测试 + 集成测试策略，确保自动化工作流程的可靠性。每个新的 MCP 工具都必须有对应的测试用例。

## Additional Technical Assumptions and Requests
- 必须保持与 Cursor IDE MCP 协议的深度集成
- 新功能必须支持现有的 10 个智能体配置
- 文档分片功能应支持 markdown-tree-parser 工具
- 状态管理使用 SQLite 进行本地存储
- 所有文件操作必须包含完整的错误处理和回滚机制
