"""
Test configuration template
Defines test settings, constants, and configuration values
"""

# Test configuration constants
TEST_CONFIG = {
    # General test settings
    'timeout': {{ timeout | default(30) }},
    'retry_count': {{ retry_count | default(3) }},
    'verbose': {{ verbose | default(True) }},
    
    # Coverage settings
    'coverage_threshold': {{ coverage_threshold | default(0.9) }},
    'coverage_fail_under': {{ coverage_fail_under | default(90) }},
    
    # Test data directories
    'test_data_dir': {{ test_data_dir | default("'tests/data'") }},
    'fixtures_dir': {{ fixtures_dir | default("'tests/fixtures'") }},
    
    {% if database_config %}
    # Database test settings
    'database': {
        'test_db_url': '{{ database_config.test_db_url }}',
        'connection_timeout': {{ database_config.connection_timeout | default(10) }},
        'cleanup_tables': {{ database_config.cleanup_tables | default(True) }},
    },
    {% endif %}
    
    {% if api_config %}
    # API test settings
    'api': {
        'base_url': '{{ api_config.base_url }}',
        'timeout': {{ api_config.timeout | default(30) }},
        'retry_attempts': {{ api_config.retry_attempts | default(3) }},
    },
    {% endif %}
}

# Test environment variables
TEST_ENV = {
    {% for env_var, value in test_environment.items() %}
    '{{ env_var }}': '{{ value }}',
    {% endfor %}
}

{% if mock_data %}
# Mock test data
MOCK_DATA = {
    {% for data_key, data_value in mock_data.items() %}
    '{{ data_key }}': {{ data_value }},
    {% endfor %}
}
{% endif %}

{% if test_constants %}
# Test constants
{% for constant_name, constant_value in test_constants.items() %}
{{ constant_name }} = {{ constant_value }}
{% endfor %}
{% endif %}

{% if pytest_marks %}
# Pytest markers
PYTEST_MARKERS = [
    {% for marker in pytest_marks %}
    '{{ marker }}',
    {% endfor %}
]
{% endif %}