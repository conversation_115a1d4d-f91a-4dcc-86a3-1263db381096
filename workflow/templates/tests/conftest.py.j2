"""
Pytest configuration file (conftest.py)
Contains shared fixtures and configuration for all tests
"""

import pytest
import asyncio
import logging
from pathlib import Path
import tempfile
import os
{% for import_line in imports %}
{{ import_line }}
{% endfor %}

# Configure logging for tests
logging.basicConfig(
    level=logging.{{ log_level | default("INFO") }},
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

{% if pytest_plugins %}
# Pytest plugins
pytest_plugins = [
    {% for plugin in pytest_plugins %}
    "{{ plugin }}",
    {% endfor %}
]
{% endif %}

{% if async_support %}
# Async test support
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

{% endif %}

{% if project_fixtures %}
# Project-specific fixtures
{% for fixture_name, fixture_config in project_fixtures.items() %}
@pytest.fixture(scope="{{ fixture_config.scope | default('function') }}")
def {{ fixture_name }}():
    """{{ fixture_config.docstring }}"""
    {% if fixture_config.setup_code %}
    # Setup
    {{ fixture_config.setup_code | indent(4, true) }}
    {% endif %}
    
    {% if fixture_config.yield_value %}
    yield {{ fixture_config.yield_value }}
    {% else %}
    yield
    {% endif %}
    
    {% if fixture_config.teardown_code %}
    # Teardown
    {{ fixture_config.teardown_code | indent(4, true) }}
    {% endif %}

{% endfor %}
{% endif %}

{% if temp_project_root %}
@pytest.fixture(scope="session")
def temp_project_root():
    """Create a temporary project root directory for tests"""
    with tempfile.TemporaryDirectory() as temp_dir:
        project_root = Path(temp_dir)
        
        # Create project structure
        {% if project_structure %}
        {% for dir_path in project_structure.directories %}
        (project_root / "{{ dir_path }}").mkdir(parents=True, exist_ok=True)
        {% endfor %}
        
        {% for file_path, content in project_structure.files.items() %}
        (project_root / "{{ file_path }}").write_text('''{{ content }}''')
        {% endfor %}
        {% endif %}
        
        yield project_root

{% endif %}

{% if database_fixture %}
@pytest.fixture(scope="session")
def test_db():
    """Create test database for the session"""
    db_path = tempfile.mktemp(suffix='.db')
    
    # Setup test database
    {% if database_setup %}
    {{ database_setup | indent(4, true) }}
    {% endif %}
    
    yield db_path
    
    # Cleanup
    try:
        os.unlink(db_path)
    except OSError:
        pass

{% endif %}

{% if custom_markers %}
# Custom pytest markers
{% for marker, description in custom_markers.items() %}
pytest.mark.{{ marker }} = pytest.mark.skip(reason="{{ description }}")
{% endfor %}

{% endif %}

{% if custom_fixtures %}
# Custom fixtures
{% for fixture_name, fixture_code in custom_fixtures.items() %}
@pytest.fixture
def {{ fixture_name }}():
    """Custom fixture: {{ fixture_name }}"""
    {{ fixture_code | indent(4, true) }}

{% endfor %}
{% endif %}

{% if test_configuration %}
# Test configuration
@pytest.fixture(autouse=True)
def configure_test_environment(monkeypatch):
    """Configure test environment variables and settings"""
    {% for env_var, value in test_configuration.environment.items() %}
    monkeypatch.setenv("{{ env_var }}", "{{ value }}")
    {% endfor %}
    
    {% if test_configuration.settings %}
    # Configure test settings
    {{ test_configuration.settings | indent(4, true) }}
    {% endif %}

{% endif %}