"""
Integration test template for Python modules
Tests component interactions and system behavior
Uses pytest framework with AAA pattern
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock
{% for import_line in imports %}
{{ import_line }}
{% endfor %}

{% if class_name %}
class Test{{ class_name }}Integration:
    """Integration test class for {{ class_name }}"""
    
    @pytest.fixture
    async def setup_environment(self):
        """Set up test environment for integration tests"""
        # Arrange test environment
        test_data = {}
        {% if setup_code %}
        {{ setup_code | indent(8, true) }}
        {% endif %}
        yield test_data
        # Cleanup after test
        {% if cleanup_code %}
        {{ cleanup_code | indent(8, true) }}
        {% endif %}
    
    {% for test_case in test_cases %}
    {% if test_case.is_async %}
    @pytest.mark.asyncio
    {% endif %}
    async def {{ test_case.name }}(self, setup_environment):
        """{{ test_case.docstring }}"""
        # Arrange - Set up components and dependencies
        {{ test_case.arrange_code | indent(8, true) }}
        
        # Act - Execute the integration scenario
        {% if test_case.is_async %}
        {{ test_case.act_code | indent(8, true) }}
        {% else %}
        result = {{ test_case.act_code.strip() }}
        {% endif %}
        
        # Assert - Verify integrated behavior
        {{ test_case.assert_code | indent(8, true) }}
    
    {% endfor %}
{% else %}
@pytest.fixture
async def integration_setup():
    """Global setup for integration tests"""
    # Setup test environment
    {% if setup_code %}
    {{ setup_code | indent(4, true) }}
    {% endif %}
    yield
    # Cleanup
    {% if cleanup_code %}
    {{ cleanup_code | indent(4, true) }}
    {% endif %}

{% for test_case in test_cases %}
{% if test_case.is_async %}
@pytest.mark.asyncio
{% endif %}
async def {{ test_case.name }}(integration_setup):
    """{{ test_case.docstring }}"""
    # Arrange - Set up integration test scenario
    {{ test_case.arrange_code | indent(4, true) }}
    
    # Act - Execute integrated functionality
    {% if test_case.is_async %}
    {{ test_case.act_code | indent(4, true) }}
    {% else %}
    result = {{ test_case.act_code.strip() }}
    {% endif %}
    
    # Assert - Verify end-to-end behavior
    {{ test_case.assert_code | indent(4, true) }}

{% endfor %}
{% endif %}
{% if database_tests %}

# Database integration tests
class TestDatabaseIntegration:
    """Tests for database interactions"""
    
    @pytest.fixture
    def db_connection(self):
        """Create test database connection"""
        # Use test database
        conn = create_test_connection()
        yield conn
        conn.close()
    
    {% for test_name, test_content in database_tests.items() %}
    def {{ test_name }}(self, db_connection):
        """{{ test_content.docstring }}"""
        {{ test_content.code | indent(8, true) }}
    
    {% endfor %}
{% endif %}

{% if api_tests %}

# API integration tests  
class TestAPIIntegration:
    """Tests for API interactions"""
    
    @pytest.fixture
    def api_client(self):
        """Create test API client"""
        from unittest.mock import Mock
        client = Mock()
        # Configure client for testing
        {% if api_client_setup %}
        {{ api_client_setup | indent(8, true) }}
        {% endif %}
        return client
    
    {% for test_name, test_content in api_tests.items() %}
    def {{ test_name }}(self, api_client):
        """{{ test_content.docstring }}"""
        {{ test_content.code | indent(8, true) }}
    
    {% endfor %}
{% endif %}