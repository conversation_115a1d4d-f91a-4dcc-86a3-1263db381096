"""
Mock setup template for Python tests
Provides common mock configurations and fixtures
"""

import pytest
from unittest.mock import Mock, MagicMock, patch, AsyncMock
{% for import_line in imports %}
{{ import_line }}
{% endfor %}

{% if mock_configurations %}
# Mock configurations
{% for mock_name, mock_config in mock_configurations.items() %}
@pytest.fixture
def {{ mock_name }}():
    """{{ mock_config.docstring }}"""
    mock = Mock()
    {% for attr, value in mock_config.attributes.items() %}
    mock.{{ attr }} = {{ value }}
    {% endfor %}
    {% if mock_config.return_value %}
    mock.return_value = {{ mock_config.return_value }}
    {% endif %}
    {% if mock_config.side_effect %}
    mock.side_effect = {{ mock_config.side_effect }}
    {% endif %}
    return mock

{% endfor %}
{% endif %}

{% if async_mocks %}
# Async mock configurations
{% for mock_name, mock_config in async_mocks.items() %}
@pytest.fixture
def {{ mock_name }}():
    """{{ mock_config.docstring }}"""
    mock = AsyncMock()
    {% for attr, value in mock_config.attributes.items() %}
    mock.{{ attr }} = {{ value }}
    {% endfor %}
    {% if mock_config.return_value %}
    mock.return_value = {{ mock_config.return_value }}
    {% endif %}
    return mock

{% endfor %}
{% endif %}

{% if file_system_mocks %}
# File system mocks
@pytest.fixture
def mock_file_system():
    """Mock file system operations"""
    with patch('pathlib.Path') as mock_path, \
         patch('builtins.open') as mock_open:
        
        # Configure Path mock
        {% for path_config in file_system_mocks.paths %}
        mock_path('{{ path_config.path }}').exists.return_value = {{ path_config.exists }}
        {% if path_config.is_dir %}
        mock_path('{{ path_config.path }}').is_dir.return_value = True
        {% endif %}
        {% if path_config.content %}
        mock_path('{{ path_config.path }}').read_text.return_value = '''{{ path_config.content }}'''
        {% endif %}
        {% endfor %}
        
        # Configure open mock
        {% for file_config in file_system_mocks.files %}
        mock_open('{{ file_config.path }}').read.return_value = '''{{ file_config.content }}'''
        {% endfor %}
        
        yield {
            'path': mock_path,
            'open': mock_open
        }

{% endif %}

{% if database_mocks %}
# Database mocks
@pytest.fixture
def mock_database():
    """Mock database operations"""
    with patch('{{ database_module }}') as mock_db:
        # Configure database mock
        {% for table, config in database_mocks.items() %}
        mock_db.{{ table }}.return_value = {{ config.return_value }}
        {% endfor %}
        yield mock_db

{% endif %}

{% if network_mocks %}
# Network mocks
@pytest.fixture
def mock_network():
    """Mock network operations"""
    with patch('requests.get') as mock_get, \
         patch('requests.post') as mock_post:
        
        # Configure HTTP mocks
        {% for endpoint, config in network_mocks.items() %}
        {% if config.method == 'GET' %}
        mock_get.return_value.status_code = {{ config.status_code }}
        mock_get.return_value.json.return_value = {{ config.response }}
        {% elif config.method == 'POST' %}
        mock_post.return_value.status_code = {{ config.status_code }}
        mock_post.return_value.json.return_value = {{ config.response }}
        {% endif %}
        {% endfor %}
        
        yield {
            'get': mock_get,
            'post': mock_post
        }

{% endif %}

{% if custom_mocks %}
# Custom mocks
{% for mock_name, mock_code in custom_mocks.items() %}
@pytest.fixture
def {{ mock_name }}():
    """Custom mock for {{ mock_name }}"""
    {{ mock_code | indent(4, true) }}

{% endfor %}
{% endif %}