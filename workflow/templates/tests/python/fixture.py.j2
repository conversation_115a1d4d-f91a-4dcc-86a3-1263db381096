"""
Test fixtures template for pytest
Provides reusable test data and setup functions
"""

import pytest
import tempfile
import os
from pathlib import Path
from datetime import datetime, timezone
{% for import_line in imports %}
{{ import_line }}
{% endfor %}

{% if data_fixtures %}
# Data fixtures
{% for fixture_name, fixture_data in data_fixtures.items() %}
@pytest.fixture
def {{ fixture_name }}():
    """{{ fixture_data.docstring }}"""
    return {{ fixture_data.data }}

{% endfor %}
{% endif %}

{% if temporary_files %}
# Temporary file fixtures
{% for fixture_name, file_config in temporary_files.items() %}
@pytest.fixture
def {{ fixture_name }}():
    """{{ file_config.docstring }}"""
    with tempfile.NamedTemporaryFile(
        mode='{{ file_config.mode | default("w") }}',
        suffix='{{ file_config.suffix | default(".tmp") }}',
        delete=False
    ) as temp_file:
        {% if file_config.content %}
        temp_file.write('''{{ file_config.content }}''')
        {% endif %}
        temp_path = temp_file.name
    
    yield temp_path
    
    # Cleanup
    try:
        os.unlink(temp_path)
    except OSError:
        pass

{% endfor %}
{% endif %}

{% if temporary_directories %}
# Temporary directory fixtures
{% for fixture_name, dir_config in temporary_directories.items() %}
@pytest.fixture
def {{ fixture_name }}():
    """{{ dir_config.docstring }}"""
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        {% if dir_config.files %}
        # Create test files
        {% for file_path, content in dir_config.files.items() %}
        (temp_path / "{{ file_path }}").parent.mkdir(parents=True, exist_ok=True)
        (temp_path / "{{ file_path }}").write_text('''{{ content }}''')
        {% endfor %}
        {% endif %}
        
        {% if dir_config.directories %}
        # Create test directories
        {% for dir_path in dir_config.directories %}
        (temp_path / "{{ dir_path }}").mkdir(parents=True, exist_ok=True)
        {% endfor %}
        {% endif %}
        
        yield temp_path

{% endfor %}
{% endif %}

{% if class_fixtures %}
# Class instance fixtures
{% for fixture_name, class_config in class_fixtures.items() %}
@pytest.fixture
def {{ fixture_name }}():
    """{{ class_config.docstring }}"""
    instance = {{ class_config.class_name }}(
        {% for arg_name, arg_value in class_config.init_args.items() %}
        {{ arg_name }}={{ arg_value }}{% if not loop.last %},{% endif %}
        {% endfor %}
    )
    
    {% if class_config.setup_code %}
    # Setup instance
    {{ class_config.setup_code | indent(4, true) }}
    {% endif %}
    
    yield instance
    
    {% if class_config.teardown_code %}
    # Cleanup instance
    {{ class_config.teardown_code | indent(4, true) }}
    {% endif %}

{% endfor %}
{% endif %}

{% if parametrized_fixtures %}
# Parametrized fixtures
{% for fixture_name, param_config in parametrized_fixtures.items() %}
@pytest.fixture(params={{ param_config.params }})
def {{ fixture_name }}(request):
    """{{ param_config.docstring }}"""
    return request.param

{% endfor %}
{% endif %}

{% if scope_fixtures %}
# Session and module scoped fixtures
{% for fixture_name, scope_config in scope_fixtures.items() %}
@pytest.fixture(scope="{{ scope_config.scope }}")
def {{ fixture_name }}():
    """{{ scope_config.docstring }}"""
    # Setup
    {{ scope_config.setup_code | indent(4, true) }}
    
    yield {{ scope_config.yield_value | default("None") }}
    
    # Teardown
    {% if scope_config.teardown_code %}
    {{ scope_config.teardown_code | indent(4, true) }}
    {% endif %}

{% endfor %}
{% endif %}

{% if database_fixtures %}
# Database fixtures
@pytest.fixture(scope="session")
def test_database():
    """Create test database for the session"""
    # Setup test database
    db_path = tempfile.mktemp(suffix='.db')
    {% if database_setup_code %}
    {{ database_setup_code | indent(4, true) }}
    {% endif %}
    
    yield db_path
    
    # Cleanup database
    try:
        os.unlink(db_path)
    except OSError:
        pass

{% endif %}

{% if async_fixtures %}
# Async fixtures
{% for fixture_name, async_config in async_fixtures.items() %}
@pytest.fixture
async def {{ fixture_name }}():
    """{{ async_config.docstring }}"""
    # Async setup
    {{ async_config.setup_code | indent(4, true) }}
    
    yield {{ async_config.yield_value | default("None") }}
    
    # Async cleanup
    {% if async_config.teardown_code %}
    {{ async_config.teardown_code | indent(4, true) }}
    {% endif %}

{% endfor %}
{% endif %}