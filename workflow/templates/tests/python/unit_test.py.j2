"""
Unit test template for Python modules
Uses pytest framework with AAA pattern (Arra<PERSON>, Act, Assert)
"""

import pytest
from unittest.mock import Mock, patch{% if mock_requirements %}, MagicMock{% endif %}
{% for import_line in imports %}
{{ import_line }}
{% endfor %}

{% if class_name %}
class Test{{ class_name }}:
    """Test class for {{ class_name }}"""
    
    @pytest.fixture
    def {{ class_name.lower() }}_instance(self):
        """Create an instance of {{ class_name }} for testing"""
        return {{ class_name }}({% if constructor_args %}{{ constructor_args }}{% endif %})
    
    {% for test_case in test_cases %}
    {% if test_case.mock_requirements %}
    {% for mock_req in test_case.mock_requirements %}
    @patch("{{ mock_req }}")
    {% endfor %}
    {% endif %}
    def {{ test_case.name }}(self{% if class_name %}, {{ class_name.lower() }}_instance{% endif %}{% if test_case.mock_requirements %}{% for mock_req in test_case.mock_requirements %}, mock_{{ loop.index0 }}{% endfor %}{% endif %}):
        """{{ test_case.docstring }}"""
        # Arrange
        {{ test_case.arrange_code | indent(8, true) }}
        
        # Act
        {{ test_case.act_code | indent(8, true) }}
        
        # Assert
        {{ test_case.assert_code | indent(8, true) }}
    
    {% endfor %}
{% else %}
{% for test_case in test_cases %}
{% if test_case.mock_requirements %}
{% for mock_req in test_case.mock_requirements %}
@patch("{{ mock_req }}")
{% endfor %}
{% endif %}
def {{ test_case.name }}({% if test_case.mock_requirements %}{% for mock_req in test_case.mock_requirements %}mock_{{ loop.index0 }}{% if not loop.last %}, {% endif %}{% endfor %}{% endif %}):
    """{{ test_case.docstring }}"""
    # Arrange
    {{ test_case.arrange_code | indent(4, true) }}
    
    # Act
    {{ test_case.act_code | indent(4, true) }}
    
    # Assert
    {{ test_case.assert_code | indent(4, true) }}

{% endfor %}
{% endif %}
{% if additional_tests %}

# Additional test cases
{% for test_name, test_content in additional_tests.items() %}
def {{ test_name }}():
    """{{ test_content.docstring }}"""
    {{ test_content.code | indent(4, true) }}

{% endfor %}
{% endif %}