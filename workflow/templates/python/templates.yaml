templates:
  class:
    description: "Python 类模板"
    parameters:
      class_name:
        type: string
        required: true
        description: "类名称"
      description:
        type: string
        required: true
        description: "类描述"
      imports:
        type: array
        required: false
        description: "导入语句列表"
      init_params:
        type: array
        required: false
        description: "初始化参数列表"
      methods:
        type: array
        required: false
        description: "方法列表"
    default_parameters:
      class_name: "ExampleClass"
      description: "示例类"
      imports: []
      init_params: []
      methods: []
    examples:
      - name: "简单类"
        parameters:
          class_name: "SimpleClass"
          description: "简单的示例类"
      - name: "带方法的类"
        parameters:
          class_name: "ComplexClass"
          description: "复杂的示例类"
          methods: ["process", "validate", "save"]

  function:
    description: "Python 函数模板"
    parameters:
      function_name:
        type: string
        required: true
        description: "函数名称"
      description:
        type: string
        required: true
        description: "函数描述"
      params:
        type: array
        required: false
        description: "参数列表"
      return_type:
        type: string
        required: false
        description: "返回类型"
      body:
        type: string
        required: false
        description: "函数体"
    default_parameters:
      function_name: "example_function"
      description: "示例函数"
      params: []
      return_type: "None"
    examples:
      - name: "简单函数"
        parameters:
          function_name: "hello_world"
          description: "打印问候语"
          return_type: "str"

  dataclass:
    description: "Python 数据类模板"
    parameters:
      class_name:
        type: string
        required: true
        description: "数据类名称"
      description:
        type: string
        required: true
        description: "数据类描述"
      fields:
        type: array
        required: true
        description: "字段列表"
      methods:
        type: array
        required: false
        description: "方法列表"
    default_parameters:
      class_name: "ExampleData"
      description: "示例数据类"
      fields: []
      methods: []
    examples:
      - name: "用户数据"
        parameters:
          class_name: "UserData"
          description: "用户信息数据类"
          fields:
            - name: "name"
              type: "str"
              comment: "用户名"
            - name: "age"
              type: "int"
              comment: "年龄"

  fastmcp_tool:
    description: "FastMCP 工具模板"
    parameters:
      tool_name:
        type: string
        required: true
        description: "工具名称"
      function_name:
        type: string
        required: true
        description: "函数名称"
      description:
        type: string
        required: true
        description: "工具描述"
      params:
        type: array
        required: false
        description: "参数列表"
      return_type:
        type: string
        required: false
        description: "返回类型"
      body:
        type: string
        required: false
        description: "工具实现体"
    default_parameters:
      tool_name: "example_tool"
      function_name: "example_tool"
      description: "示例 FastMCP 工具"
      params: []
      return_type: "Dict[str, Any]"
    examples:
      - name: "文件处理工具"
        parameters:
          tool_name: "process_file"
          function_name: "process_file"
          description: "处理文件的工具"
          params: ["file_path: str"]
          return_type: "Dict[str, Any]"

  module:
    description: "Python 模块模板"
    parameters:
      description:
        type: string
        required: true
        description: "模块描述"
      imports:
        type: array
        required: false
        description: "导入语句列表"
      constants:
        type: array
        required: false
        description: "常量定义列表"
      classes:
        type: array
        required: false
        description: "类定义列表"
      functions:
        type: array
        required: false
        description: "函数定义列表"
      main_block:
        type: string
        required: false
        description: "主程序块"
    default_parameters:
      description: "示例模块"
      imports: []
      constants: []
      classes: []
      functions: []
    examples:
      - name: "工具模块"
        parameters:
          description: "实用工具模块"
          imports: ["import os", "from typing import Dict, List"]
          functions:
            - name: "get_file_info"
              description: "获取文件信息"
              params: ["file_path: str"]
              return_type: "Dict[str, Any]"
