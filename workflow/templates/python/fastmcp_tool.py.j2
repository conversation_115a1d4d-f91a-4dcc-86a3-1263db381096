"""
{{ description }}
FastMCP 工具实现
"""

import logging
from typing import Dict, List, Optional, Any
from fastmcp import FastMCP
{% if imports %}
{% for import in imports %}
{{ import }}
{% endfor %}
{% endif %}

logger = logging.getLogger(__name__)


@FastMCP.tool("{{ tool_name }}")
def {{ function_name }}({% if params %}{{ params|join(', ') }}{% endif %}){% if return_type %} -> {{ return_type }}{% endif %}:
    """
    {{ description }}
    {% if params %}
    
    Args:
    {% for param in params %}
        {{ param }}: {{ param }} 参数
    {% endfor %}
    {% endif %}
    {% if return_type %}
    
    Returns:
        {{ return_type }}: 返回值描述
    {% endif %}
    """
    try:
        logger.info(f"Executing {{ tool_name }} tool")
        
        {% if body %}
        {{ body }}
        {% else %}
        # TODO: 实现 {{ tool_name }} 工具逻辑
        result = {
            "success": True,
            "message": "{{ tool_name }} executed successfully"
        }
        
        logger.info(f"{{ tool_name }} completed successfully")
        return result
        {% endif %}
        
    except Exception as e:
        error_msg = f"{{ tool_name }} failed: {str(e)}"
        logger.error(error_msg)
        return {
            "success": False,
            "error": error_msg
        }
