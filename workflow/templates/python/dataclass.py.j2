"""
{{ description }}
"""

from dataclasses import dataclass, field, asdict
from datetime import datetime
from typing import Dict, List, Optional, Any
{% if imports %}
{% for import in imports %}
{{ import }}
{% endfor %}
{% endif %}


@dataclass
class {{ class_name }}:
    """{{ description }}"""
    {% if fields %}
    {% for field in fields %}
    {{ field.name }}: {{ field.type }}{% if field.default %} = {{ field.default }}{% elif field.default_factory %} = field(default_factory={{ field.default_factory }}){% endif %}{% if field.comment %}  # {{ field.comment }}{% endif %}
    {% endfor %}
    {% endif %}
    
    {% if methods %}
    {% for method in methods %}
    def {{ method }}(self{% if method_params and method_params[method] %}, {{ method_params[method]|join(', ') }}{% endif %}){% if method_returns and method_returns[method] %} -> {{ method_returns[method] }}{% endif %}:
        """{{ method_descriptions[method] if method_descriptions and method_descriptions[method] else method + ' 方法' }}"""
        {% if method_bodies and method_bodies[method] %}
        {{ method_bodies[method] }}
        {% else %}
        # TODO: 实现 {{ method }} 方法
        pass
        {% endif %}
    
    {% endfor %}
    {% endif %}
    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return asdict(self)
