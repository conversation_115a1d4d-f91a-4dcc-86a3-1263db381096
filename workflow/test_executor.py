from dataclasses import dataclass
import subprocess
import json
import os
import tempfile
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)


@dataclass
class TestExecutionResult:
    success: bool
    return_code: int
    stdout: str
    stderr: str
    junit_report: Dict[str, Any]


class TestExecutor:
    """
    Minimal TestExecutor: runs pytest on given test files, collects output and optional junit xml.
    This is a lightweight, dependency-free implementation that shells out to pytest.
    """

    def __init__(self, pytest_path: str = "pytest"):
        self.pytest_path = pytest_path

    def run_tests(self, test_paths: List[str], extra_args: List[str]=None, cwd: str = None) -> TestExecutionResult:
        args = [self.pytest_path]
        if extra_args:
            args += extra_args
        args += test_paths
        # ensure junit report to temp file
        report_file = tempfile.NamedTemporaryFile(prefix="pytest-junit-", suffix=".xml", delete=False)
        report_path = report_file.name
        report_file.close()
        args += ["--junit-xml", report_path, "-q"]
        logger.info("Running pytest: %s", args)
        proc = subprocess.Popen(args, stdout=subprocess.PIPE, stderr=subprocess.PIPE, cwd=cwd)
        out, err = proc.communicate()
        stdout = out.decode("utf-8", errors="replace")
        stderr = err.decode("utf-8", errors="replace")
        success = proc.returncode == 0
        junit_report = {}
        try:
            if os.path.exists(report_path):
                with open(report_path, "r", encoding="utf-8") as f:
                    junit_report["xml"] = f.read()
        except Exception as e:
            logger.exception("Failed to read junit report: %s", e)
        finally:
            try:
                os.unlink(report_path)
            except Exception:
                pass

        return TestExecutionResult(
            success=success,
            return_code=proc.returncode,
            stdout=stdout,
            stderr=stderr,
            junit_report=junit_report
        )

    def run_tests_with_coverage(self, test_paths: List[str], cov_report_path: str = None, extra_args: List[str]=None, cwd: str=None) -> TestExecutionResult:
        """
        Run pytest with coverage enabled. Requires pytest-cov installed.
        Generates a junit xml report and (optionally) a coverage xml at cov_report_path.
        Returns coverage xml content in junit_report['coverage_xml'] when available.
        """
        args = [self.pytest_path]
        if extra_args:
            args += extra_args
        # default to collecting coverage for project root and produce xml
        args += ["--cov", ".", "--cov-report", "xml"]
        if cov_report_path:
            # place coverage xml at requested path
            args += ["--cov-report", f"xml:{cov_report_path}"]
        args += test_paths
        report_file = tempfile.NamedTemporaryFile(prefix="pytest-junit-", suffix=".xml", delete=False)
        report_path = report_file.name
        report_file.close()
        args += ["--junit-xml", report_path, "-q"]
        logger.info("Running pytest with coverage: %s", args)
        proc = subprocess.Popen(args, stdout=subprocess.PIPE, stderr=subprocess.PIPE, cwd=cwd)
        out, err = proc.communicate()
        stdout = out.decode("utf-8", errors="replace")
        stderr = err.decode("utf-8", errors="replace")
        success = proc.returncode == 0
        junit_report: Dict[str, Any] = {}
        coverage_xml = None

        try:
            if os.path.exists(report_path):
                with open(report_path, "r", encoding="utf-8") as f:
                    junit_report["xml"] = f.read()
        except Exception as e:
            logger.exception("Failed to read junit report: %s", e)
        finally:
            try:
                os.unlink(report_path)
            except Exception:
                pass

        # If a coverage report path was specified, try to read the coverage XML and include it in the result.
        if cov_report_path:
            try:
                cov_path = cov_report_path
                # If cov_report_path is relative, resolve against cwd or current working dir
                if not os.path.isabs(cov_path) and cwd:
                    cov_path = os.path.join(cwd, cov_report_path)
                if os.path.exists(cov_path):
                    with open(cov_path, "r", encoding="utf-8") as f:
                        coverage_xml = f.read()
                else:
                    # common fallback: coverage.xml in current working directory
                    fallback = os.path.join(cwd or ".", "coverage.xml")
                    if os.path.exists(fallback):
                        with open(fallback, "r", encoding="utf-8") as f:
                            coverage_xml = f.read()
            except Exception as e:
                logger.exception("Failed to read coverage xml: %s", e)

        if coverage_xml:
            junit_report["coverage_xml"] = coverage_xml

        return TestExecutionResult(
            success=success,
            return_code=proc.returncode,
            stdout=stdout,
            stderr=stderr,
            junit_report=junit_report
        )