"""
Error handling components for Story 4.2: 错误恢复与重试机制

提供：
- ErrorClassifier: 将异常映射为 ErrorRecord（分类与严重度评估）
- RetryManager: 管理重试策略（指数退避、超时、次数限制）
- execute_with_retry: 将函数包装为自动重试执行（支持同步与异步函数）

注意：
- 使用标准 logging 模块进行结构化日志记录（轻量实现）
- 保持接口简单以便后续集成到 WorkflowExecutor / StateEngine
"""
from __future__ import annotations

import asyncio
import logging
import traceback
import time
from typing import Any, Callable, Dict, Optional

from .models import ErrorRecord, RetryPolicy

logger = logging.getLogger(__name__)


class ErrorClassifier:
    """
    将异常与上下文转换为 ErrorRecord 的辅助类。
    可扩展以支持更复杂的模式识别与历史驱动分类。
    """

    TEMPORARY_ERRORS = ("ConnectionError", "TimeoutError", "BrokenPipeError")
    PERMANENT_ERRORS = ("FileNotFoundError", "PermissionError", "ValueError")

    @classmethod
    def classify_error(cls, exc: Exception, context: Optional[Dict[str, Any]] = None, phase: str = "unknown", component: str = "unknown") -> ErrorRecord:
        """
        基本分类逻辑：
        - 检查异常类型名称匹配临时或永久错误列表
        - 对于未知异常，默认为 system_error 并标记为 high severity
        """
        context = context or {}
        exc_name = type(exc).__name__
        msg = str(exc)
        stack = "".join(traceback.format_exception(type(exc), exc, exc.__traceback__))

        if exc_name in cls.TEMPORARY_ERRORS:
            error_type = "temporary"
            severity = "medium"
        elif exc_name in cls.PERMANENT_ERRORS:
            error_type = "permanent"
            severity = "high"
        else:
            # 默认分类，可根据消息/上下文增强
            error_type = "system_error"
            severity = "high"

        # 将用户取消识别为 user_error（如果上下文表明）
        if "user_cancelled" in context and context.get("user_cancelled"):
            error_type = "user_error"
            severity = "low"

        err = ErrorRecord.new(
            error_type=error_type,
            severity=severity,
            phase=phase,
            component=component,
            error_message=msg,
            context=context,
        )
        err.stack_trace = stack
        return err

    @classmethod
    def evaluate_severity(cls, error_record: ErrorRecord) -> str:
        """
        可以放置更复杂的严重度评估逻辑（例如根据组件、频率、历史记录）。
        当前直接返回已有字段以便扩展。
        """
        return error_record.severity


class RetryManager:
    """
    管理重试执行的策略：
    - 支持指数退避（由 RetryPolicy.compute_delay 提供）
    - 支持总超时、最大重试次数
    - 支持基于 ErrorRecord 的 should_retry 判断
    """

    def __init__(self, policy: Optional[RetryPolicy] = None):
        self.policy = policy or RetryPolicy()

    def should_retry(self, error: ErrorRecord) -> bool:
        """
        判断是否应当重试：
        - 错误类型在重试条件内 (默认 'temporary')
        - 当前重试次数小于 max_retries
        - （可扩展）检查外部条件，如系统负载或熔断状态
        """
        if error.retry_count >= self.policy.max_retries:
            return False
        if error.error_type in self.policy.retry_conditions:
            return True
        return False

    async def execute_with_retry_async(self, func: Callable[..., Any], *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """
        支持异步函数的重试包装。
        timeout 是总重试窗口（秒）。
        """
        start = time.time()
        attempt = 0
        last_exception = None
        while True:
            try:
                result = await func(*args, **kwargs)
                return result
            except Exception as exc:
                last_exception = exc
                err = ErrorClassifier.classify_error(exc, context=kwargs.get("context"), phase=kwargs.get("phase", "unknown"), component=kwargs.get("component", "unknown"))
                err.retry_count = attempt
                logger.error("RetryManager caught exception (async): %s | attempt=%d | error_id=%s", str(exc), attempt, err.error_id, extra={"error_record": err.to_dict()})
                if not self.should_retry(err):
                    logger.debug("Not retrying (async): %s", err.to_dict())
                    raise
                # 检查超时
                if timeout is not None and (time.time() - start) > timeout:
                    logger.debug("Retry timeout exceeded (async)")
                    raise
                delay = self.policy.compute_delay(attempt)
                attempt += 1
                await asyncio.sleep(delay)

    def execute_with_retry(self, func: Callable[..., Any], *args, timeout: Optional[float] = None, **kwargs) -> Any:
        """
        同步函数的重试包装。对于 coroutine 会转为 asyncio 运行。
        kwargs 可包含 context/phase/component，用于错误分类。
        """
        if asyncio.iscoroutinefunction(func):
            # 在同步上下文中运行 async 函数
            return asyncio.run(self.execute_with_retry_async(func, *args, timeout=timeout, **kwargs))

        start = time.time()
        attempt = 0
        last_exception = None
        while True:
            try:
                return func(*args, **kwargs)
            except Exception as exc:
                last_exception = exc
                err = ErrorClassifier.classify_error(exc, context=kwargs.get("context"), phase=kwargs.get("phase", "unknown"), component=kwargs.get("component", "unknown"))
                err.retry_count = attempt
                logger.error("RetryManager caught exception: %s | attempt=%d | error_id=%s", str(exc), attempt, err.error_id, extra={"error_record": err.to_dict()})
                if not self.should_retry(err):
                    logger.debug("Not retrying: %s", err.to_dict())
                    raise
                if timeout is not None and (time.time() - start) > timeout:
                    logger.debug("Retry timeout exceeded")
                    raise
                delay = self.policy.compute_delay(attempt)
                attempt += 1
                time.sleep(delay)


def execute_with_retry(func: Callable[..., Any], *args, retry_policy: Optional[RetryPolicy] = None, timeout: Optional[float] = None, **kwargs) -> Any:
    """
    便捷函数：使用 RetryManager 执行具有重试能力的调用。
    """
    manager = RetryManager(policy=retry_policy)
    return manager.execute_with_retry(func, *args, timeout=timeout, **kwargs)