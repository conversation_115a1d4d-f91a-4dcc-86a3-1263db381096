from typing import Dict, Any, List
from pathlib import Path
import json
import logging
import os

logger = logging.getLogger(__name__)


def scan_project_structure(project_path: str) -> Dict[str, Any]:
    """
    扫描项目目录，识别 docs/ 中的核心文档、prd/ 和 architecture/ 分片文件，以及 epics/stories 文件。
    返回结构化的扫描结果字典。
    """
    try:
        p = Path(project_path)
        
        # Validate project path exists and is accessible
        if not p.exists():
            logger.warning("Project path does not exist: %s", project_path)
            return {
                "project_path": str(p),
                "exists": False,
                "error": f"Project path does not exist: {project_path}",
                "docs": {"exists": False, "files": []},
                "core_docs": {"brief.md": False, "prd.md": False, "architecture.md": False},
                "prd_shards": [],
                "architecture_shards": [],
                "epics": [],
                "stories": [],
            }
            
        if not p.is_dir():
            logger.warning("Project path is not a directory: %s", project_path)
            return {
                "project_path": str(p),
                "exists": True,
                "error": f"Project path is not a directory: {project_path}",
                "docs": {"exists": False, "files": []},
                "core_docs": {"brief.md": False, "prd.md": False, "architecture.md": False},
                "prd_shards": [],
                "architecture_shards": [],
                "epics": [],
                "stories": [],
            }
        
        # Check read permissions
        if not os.access(p, os.R_OK):
            logger.error("No read permission for project path: %s", project_path)
            return {
                "project_path": str(p),
                "exists": True,
                "error": f"No read permission for project path: {project_path}",
                "docs": {"exists": False, "files": []},
                "core_docs": {"brief.md": False, "prd.md": False, "architecture.md": False},
                "prd_shards": [],
                "architecture_shards": [],
                "epics": [],
                "stories": [],
            }
            
    except OSError as e:
        logger.exception("OS error accessing project path %s: %s", project_path, e)
        return {
            "project_path": project_path,
            "exists": False,
            "error": f"OS error accessing project path: {str(e)}",
            "docs": {"exists": False, "files": []},
            "core_docs": {"brief.md": False, "prd.md": False, "architecture.md": False},
            "prd_shards": [],
            "architecture_shards": [],
            "epics": [],
            "stories": [],
        }
        
    result: Dict[str, Any] = {
        "project_path": str(p),
        "exists": p.exists(),
        "docs": {"exists": False, "files": []},
        "core_docs": {"brief.md": False, "prd.md": False, "architecture.md": False},
        "prd_shards": [],
        "architecture_shards": [],
        "epics": [],
        "stories": [],
    }

    try:
        docs_dir = p / "docs"
        result["docs"]["exists"] = docs_dir.exists() and docs_dir.is_dir()
        if not result["docs"]["exists"]:
            logger.info("No docs directory found at %s", docs_dir)
            return result

        # List files in docs root with error handling
        try:
            for f in docs_dir.iterdir():
                if f.is_file():
                    name = f.name
                    result["docs"]["files"].append(name)
                    if name in result["core_docs"]:
                        result["core_docs"][name] = True
        except (OSError, PermissionError) as e:
            logger.warning("Error listing docs directory %s: %s", docs_dir, e)
            result["docs"]["files"] = []

        # scan prd/ and architecture/ subfolders with error handling
        def safe_scan_subdir(subdir_name: str, result_key: str) -> None:
            try:
                subdir = docs_dir / subdir_name
                if subdir.exists() and subdir.is_dir():
                    for f in subdir.rglob("*.md"):
                        result[result_key].append(str(f.relative_to(p)))
            except (OSError, PermissionError) as e:
                logger.warning("Error scanning %s directory: %s", subdir_name, e)
            except Exception as e:
                logger.exception("Unexpected error scanning %s directory: %s", subdir_name, e)

        safe_scan_subdir("prd", "prd_shards")
        safe_scan_subdir("architecture", "architecture_shards") 
        safe_scan_subdir("epics", "epics")
        safe_scan_subdir("stories", "stories")

    except Exception as e:
        logger.exception("Error scanning project structure: %s", e)
        result["error"] = str(e)

    return result


def detect_current_stage(scan_result: Dict[str, Any]) -> str:
    """
    基于 scan_project_structure 的结果，使用简单规则判断当前工作流阶段。
    返回阶段标识符，例如：document_processing, epic_creation, story_development, implementation
    """
    # If docs missing -> unknown / setup
    if not scan_result.get("exists", False) or not scan_result.get("docs", {}).get("exists", False):
        return "no_docs"

    core = scan_result.get("core_docs", {})
    # If core docs not fully present, still document processing
    if not all(core.get(k, False) for k in ("brief.md", "prd.md", "architecture.md")):
        return "document_processing"

    # If epics not present but prd shards present -> epic_creation
    if not scan_result.get("epics"):
        if scan_result.get("prd_shards"):
            return "epic_creation"
        # fallback
        return "document_validated"

    # If stories missing -> story_development
    if scan_result.get("epics") and not scan_result.get("stories"):
        return "story_development"

    # If stories exist but no indicators of implementation (e.g., code files) -> ready_for_implementation
    if scan_result.get("stories"):
        return "ready_for_implementation"

    return "unknown"


def generate_status_report(workflow_state: Dict[str, Any], scan_result: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成一个简洁的状态报告，包含当前阶段、核心文档存在性、epic/story 数量与下一步建议。
    workflow_state 可以是 WorkflowState.to_dict() 的结果或任意包含 current_stage 字段的 dict。
    """
    report: Dict[str, Any] = {}
    current_stage = workflow_state.get("current_stage") if isinstance(workflow_state, dict) else None
    detected_stage = detect_current_stage(scan_result)

    report["project_path"] = scan_result.get("project_path")
    report["detected_stage"] = detected_stage
    report["workflow_current_stage"] = current_stage or detected_stage
    report["core_docs"] = scan_result.get("core_docs", {})
    report["prd_shards_count"] = len(scan_result.get("prd_shards", []))
    report["architecture_shards_count"] = len(scan_result.get("architecture_shards", []))
    report["epics_count"] = len(scan_result.get("epics", []))
    report["stories_count"] = len(scan_result.get("stories", []))
    report["next_action_suggestion"] = _suggest_next_action(detected_stage)

    return report


def _suggest_next_action(stage: str) -> str:
    mapping = {
        "no_docs": "Add docs/ with brief.md, prd.md, architecture.md",
        "document_processing": "Complete core documents (brief/prd/architecture) and run sharding",
        "document_validated": "Create epics from PRD shards",
        "epic_creation": "Generate epics files from PRD shards",
        "story_development": "Generate stories for epics",
        "ready_for_implementation": "Start implementation (Dev Agent) for stories",
        "unknown": "Inspect project structure manually",
    }
    return mapping.get(stage, "Inspect project structure manually")


# -- Lightweight persistence & checkpointing utilities (MVP) --
def _state_dir(project_path: str) -> Path:
    """Return the directory used to store workflow runtime state for this project."""
    p = Path(project_path)
    sd = p / ".workflow_state"
    sd.mkdir(parents=True, exist_ok=True)
    return sd


def save_execution_state(project_path: str, workflow_state: Dict[str, Any]) -> str:
    """
    Persist the workflow_state as a JSON file under .workflow_state/<workflow_id>.json
    Returns the path to the saved file.
    """
    try:
        sd = _state_dir(project_path)
        wid = workflow_state.get("workflow_id", f"wf-{int(__import__('time').time())}")
        path = sd / f"{wid}.json"
        with open(path, "w", encoding="utf-8") as f:
            json.dump(workflow_state, f, indent=2, ensure_ascii=False)
        return str(path)
    except Exception as e:
        logger.exception("Failed to save execution state: %s", e)
        raise


def load_execution_state(project_path: str, workflow_id: str) -> Dict[str, Any]:
    """
    Load a previously saved workflow_state by workflow_id.
    Raises FileNotFoundError if not present.
    """
    sd = _state_dir(project_path)
    path = sd / f"{workflow_id}.json"
    if not path.exists():
        raise FileNotFoundError(f"Workflow state not found: {path}")
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)


def save_checkpoint(project_path: str, workflow_id: str, checkpoint_name: str, checkpoint_data: Dict[str, Any]) -> str:
    """
    Save a checkpoint (arbitrary dict) under .workflow_state/<workflow_id>__<checkpoint_name>.checkpoint.json
    Returns the checkpoint file path.
    """
    try:
        sd = _state_dir(project_path)
        fname = f"{workflow_id}__{checkpoint_name}.checkpoint.json"
        path = sd / fname
        with open(path, "w", encoding="utf-8") as f:
            json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
        return str(path)
    except Exception as e:
        logger.exception("Failed to save checkpoint: %s", e)
        raise


def load_checkpoint(project_path: str, workflow_id: str, checkpoint_name: str) -> Dict[str, Any]:
    """
    Load a checkpoint previously saved.
    """
    sd = _state_dir(project_path)
    fname = f"{workflow_id}__{checkpoint_name}.checkpoint.json"
    path = sd / fname
    if not path.exists():
        raise FileNotFoundError(f"Checkpoint not found: {path}")
    with open(path, "r", encoding="utf-8") as f:
        return json.load(f)


def list_checkpoints(project_path: str, workflow_id: str) -> List[str]:
    """
    List checkpoint files for a workflow_id (returns filenames).
    """
    sd = _state_dir(project_path)
    prefix = f"{workflow_id}__"
    files = []
    for p in sd.iterdir():
        if p.is_file() and p.name.startswith(prefix) and p.name.endswith(".checkpoint.json"):
            files.append(str(p.name))
    return files


def restore_from_checkpoint(project_path: str, workflow_id: str, checkpoint_name: str) -> Dict[str, Any]:
    """
    Restore workflow_state from a checkpoint payload.
    This function assumes the checkpoint contains a serialized workflow_state snapshot under key 'workflow_state'.
    Returns restored workflow_state dict.
    """
    try:
        cp = load_checkpoint(project_path, workflow_id, checkpoint_name)
        workflow_state = cp.get("workflow_state")
        if not workflow_state:
            raise ValueError("Checkpoint does not contain 'workflow_state' key")
        # Optionally persist as current state
        save_execution_state(project_path, workflow_state)
        return workflow_state
    except Exception as e:
        logger.exception("Failed to restore from checkpoint: %s", e)
        raise


def start_greenfield_workflow(project_path: str, auto_confirm: bool = False) -> Dict[str, Any]:
    """
    Start a lightweight 'greenfield' workflow orchestration.

    Responsibilities (lightweight MVP implementation):
    - Scan the project structure to detect current stage.
    - Produce an initial workflow_state dict (minimal) and a status report.
    - If auto_confirm is False, return with detected_stage and next action suggestion so caller may prompt user.
    - If auto_confirm is True, mark workflow as advanced to the suggested next stage where reasonable.

    This is intentionally minimal and synchronous — it provides a clear entrypoint
    that higher-level executors or MCP tools can call. The full async executor and
    persistence/checkpointing will be implemented in workflow/workflow_executor.py.
    """
    try:
        logger.info("Starting greenfield workflow for project: %s (auto_confirm=%s)", project_path, auto_confirm)
        scan = scan_project_structure(project_path)
        detected = detect_current_stage(scan)

        # Minimal workflow_state representation (keeps compatible with generate_status_report)
        workflow_state = {
            "workflow_id": f"wf-{int(__import__('time').time())}",
            "current_stage": detected,
            "project_path": project_path,
            "created_at": __import__("datetime").datetime.utcnow().isoformat() + "Z",
            "updated_at": __import__("datetime").datetime.utcnow().isoformat() + "Z",
        }

        report = generate_status_report(workflow_state, scan)

        # If auto_confirm is requested and the detected stage has a clear next action,
        # we can mark a simple state transition for the MVP (does not perform heavy work).
        if auto_confirm:
            suggestion = report.get("next_action_suggestion", "")
            logger.info("Auto-confirm enabled. Suggested action: %s", suggestion)
            # Simple heuristic: move document_processing -> epic_creation when prd_shards exist
            if detected == "document_processing" and scan.get("prd_shards"):
                workflow_state["current_stage"] = "epic_creation"
            elif detected == "epic_creation" and not scan.get("epics"):
                # leave as epic_creation; real executor will create epics
                workflow_state["current_stage"] = "epic_creation"
            elif detected == "story_development":
                workflow_state["current_stage"] = "story_development"

            workflow_state["updated_at"] = __import__("datetime").datetime.utcnow().isoformat() + "Z"
            report = generate_status_report(workflow_state, scan)

        logger.info("Greenfield workflow started: %s", workflow_state["workflow_id"])
        return {"workflow_state": workflow_state, "report": report, "scan": scan}
    except Exception as e:
        logger.exception("Failed to start greenfield workflow for %s: %s", project_path, e)
        return {"error": str(e)}