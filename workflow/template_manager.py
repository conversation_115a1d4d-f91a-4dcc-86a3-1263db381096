"""
代码模板管理器
提供模板的加载、渲染和管理功能
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Any
import jinja2
import yaml

logger = logging.getLogger(__name__)


class TemplateManager:
    """代码模板管理器"""
    
    def __init__(self, template_root: str):
        """
        初始化模板管理器
        
        Args:
            template_root: 模板根目录路径
        """
        self.template_root = Path(template_root)
        self.jinja_env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(str(self.template_root)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 加载模板配置
        self.template_configs = self._load_template_configs()
        
        logger.info(f"TemplateManager initialized with {len(self.template_configs)} template configurations")
    
    def _load_template_configs(self) -> Dict[str, Dict[str, Any]]:
        """
        加载模板配置文件
        
        Returns:
            Dict[str, Dict[str, Any]]: 模板配置字典
        """
        configs = {}
        
        # 查找所有 .yaml 配置文件
        for config_file in self.template_root.rglob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                
                if isinstance(config_data, dict) and 'templates' in config_data:
                    for template_name, template_config in config_data['templates'].items():
                        configs[template_name] = template_config
                        
            except Exception as e:
                logger.warning(f"Failed to load template config {config_file}: {str(e)}")
        
        return configs
    
    def get_available_templates(self, language: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        获取可用的模板列表
        
        Args:
            language: 编程语言过滤器
            
        Returns:
            List[Dict[str, Any]]: 可用模板列表
        """
        templates = []
        
        # 扫描模板文件
        for template_file in self.template_root.rglob("*.j2"):
            relative_path = template_file.relative_to(self.template_root)
            parts = relative_path.parts
            
            if len(parts) >= 2:
                template_language = parts[0]
                template_name = parts[-1].replace('.j2', '')
                
                if language and template_language != language:
                    continue
                
                template_info = {
                    'name': template_name,
                    'language': template_language,
                    'path': str(relative_path),
                    'full_path': str(template_file),
                    'config': self.template_configs.get(template_name, {})
                }
                
                templates.append(template_info)
        
        return templates
    
    def render_template(self, template_name: str, language: str, parameters: Dict[str, Any]) -> str:
        """
        渲染模板
        
        Args:
            template_name: 模板名称
            language: 编程语言
            parameters: 模板参数
            
        Returns:
            str: 渲染后的内容
        """
        try:
            template_path = f"{language}/{template_name}.j2"
            template = self.jinja_env.get_template(template_path)
            
            # 合并默认参数和用户参数
            config = self.template_configs.get(template_name, {})
            default_params = config.get('default_parameters', {})
            merged_params = {**default_params, **parameters}
            
            return template.render(**merged_params)
            
        except Exception as e:
            logger.error(f"Failed to render template {template_name} for {language}: {str(e)}")
            raise
    
    def create_template_from_code(self, code: str, template_name: str, language: str) -> bool:
        """
        从现有代码创建模板
        
        Args:
            code: 源代码
            template_name: 模板名称
            language: 编程语言
            
        Returns:
            bool: 是否成功创建模板
        """
        try:
            template_dir = self.template_root / language
            template_dir.mkdir(parents=True, exist_ok=True)
            
            template_file = template_dir / f"{template_name}.j2"
            
            # 简单的模板化处理
            template_content = self._convert_code_to_template(code)
            
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            logger.info(f"Created template {template_name} for {language}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create template {template_name}: {str(e)}")
            return False
    
    def _convert_code_to_template(self, code: str) -> str:
        """
        将代码转换为模板
        
        Args:
            code: 源代码
            
        Returns:
            str: 模板内容
        """
        # 这是一个简化的实现，实际应用中可能需要更复杂的逻辑
        template_content = code
        
        # 替换常见的可参数化内容
        replacements = [
            ('class TestClass', 'class {{ class_name }}'),
            ('def test_function', 'def {{ function_name }}'),
            ('# Test comment', '# {{ comment }}'),
            ('"""Test docstring"""', '"""{{ description }}"""'),
        ]
        
        for old, new in replacements:
            template_content = template_content.replace(old, new)
        
        return template_content
    
    def validate_template(self, template_name: str, language: str) -> Dict[str, Any]:
        """
        验证模板
        
        Args:
            template_name: 模板名称
            language: 编程语言
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        result = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        try:
            template_path = f"{language}/{template_name}.j2"
            template = self.jinja_env.get_template(template_path)
            
            # 尝试使用默认参数渲染
            config = self.template_configs.get(template_name, {})
            default_params = config.get('default_parameters', {})
            
            if default_params:
                try:
                    template.render(**default_params)
                except Exception as e:
                    result['valid'] = False
                    result['errors'].append(f"Template rendering failed with default parameters: {str(e)}")
            else:
                result['warnings'].append("No default parameters defined for template")
            
        except jinja2.TemplateNotFound:
            result['valid'] = False
            result['errors'].append(f"Template {template_name} not found for language {language}")
        except Exception as e:
            result['valid'] = False
            result['errors'].append(f"Template validation failed: {str(e)}")
        
        return result
    
    def get_template_parameters(self, template_name: str, language: str) -> Dict[str, Any]:
        """
        获取模板参数信息

        Args:
            template_name: 模板名称
            language: 编程语言

        Returns:
            Dict[str, Any]: 模板参数信息
        """
        try:
            template_path = f"{language}/{template_name}.j2"

            # 直接读取模板文件内容
            template_file = self.template_root / template_path
            with open(template_file, 'r', encoding='utf-8') as f:
                template_source = f.read()

            # 分析模板中的变量（改进的正则表达式方法）
            import re
            # 匹配 {{ variable }} 和 {% for item in variable %}
            variable_patterns = [
                r'\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)',  # {{ variable }}
                r'\{\%\s*for\s+\w+\s+in\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # {% for item in variable %}
            ]

            variables = set()
            for pattern in variable_patterns:
                variables.update(re.findall(pattern, template_source))

            config = self.template_configs.get(template_name, {})
            parameter_info = config.get('parameters', {})

            parameters = {}
            for var in variables:
                parameters[var] = parameter_info.get(var, {
                    'type': 'string',
                    'required': True,
                    'description': f'Parameter {var}'
                })

            return {
                'parameters': parameters,
                'default_parameters': config.get('default_parameters', {}),
                'description': config.get('description', ''),
                'examples': config.get('examples', [])
            }

        except Exception as e:
            logger.error(f"Failed to get template parameters for {template_name}: {str(e)}")
            return {
                'parameters': {},
                'default_parameters': {},
                'description': '',
                'examples': []
            }
    
    def create_custom_template(self, template_name: str, language: str, 
                             template_content: str, config: Dict[str, Any]) -> bool:
        """
        创建自定义模板
        
        Args:
            template_name: 模板名称
            language: 编程语言
            template_content: 模板内容
            config: 模板配置
            
        Returns:
            bool: 是否成功创建
        """
        try:
            # 创建模板文件
            template_dir = self.template_root / language
            template_dir.mkdir(parents=True, exist_ok=True)
            
            template_file = template_dir / f"{template_name}.j2"
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            # 保存配置
            config_file = template_dir / f"{template_name}.yaml"
            config_data = {
                'templates': {
                    template_name: config
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            # 更新内存中的配置
            self.template_configs[template_name] = config
            
            logger.info(f"Created custom template {template_name} for {language}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create custom template {template_name}: {str(e)}")
            return False
