import asyncio
import logging
import uuid
from collections import OrderedDict
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional

from workflow.models import SharedContext, ContextCache

logger = logging.getLogger(__name__)


class _SimpleLRUCache:
    """
    简单 LRU 实现（线程安全假设由调用方的 asyncio 语境保证）。
    使用 OrderedDict 维护 LRU 顺序。
    """

    def __init__(self, max_items: int = 128):
        self.max_items = max_items
        self._store: "OrderedDict[str, ContextCache]" = OrderedDict()

    def get(self, key: str) -> Optional[ContextCache]:
        item = self._store.get(key)
        if not item:
            return None
        # move to end -> most recently used
        self._store.move_to_end(key)
        item.touch()
        return item

    def put(self, key: str, content: Any, expiry_seconds: Optional[int] = None) -> ContextCache:
        now = datetime.utcnow()
        size_bytes = len(str(content).encode("utf-8")) if content is not None else 0
        expiry_time = None
        if expiry_seconds:
            expiry_time = now + timedelta(seconds=expiry_seconds)
        cache_item = ContextCache(
            cache_key=key,
            content=content,
            size_bytes=size_bytes,
            hit_count=0,
            created_at=now,
            last_accessed=now,
            expiry_time=expiry_time,
        )
        self._store[key] = cache_item
        self._store.move_to_end(key)
        # evict if over capacity
        while len(self._store) > self.max_items:
            evicted_key, _ = self._store.popitem(last=False)
            logger.debug("LRU cache evicted key: %s", evicted_key)
        return cache_item

    def stats(self) -> Dict[str, Any]:
        total_size = sum(item.size_bytes for item in self._store.values())
        total_items = len(self._store)
        hits = sum(item.hit_count for item in self._store.values())
        return {"items": total_items, "total_size_bytes": total_size, "total_hits": hits}

    def keys(self):
        return list(self._store.keys())

    def remove(self, key: str) -> None:
        if key in self._store:
            del self._store[key]


class ContextLoader:
    """
    Minimal ContextLoader 实现，用于 Story 2.2 的首要验收目标。

    特性：
    - 异步 load_context_for_agent(agent_id, task_type) -> SharedContext
    - 内存 LRU 缓存（L1），缓存 SharedContext.to_dict() 的结果
    - get_context_cache_stats() 返回简单统计
    - update_shared_context(context_id, updates) 的最小实现
    - sync_context_between_agents(from_agent, to_agent) 的最小实现（返回 True）
    """

    def __init__(self, cache_size: int = 64):
        self._l1 = _SimpleLRUCache(max_items=cache_size)
        # 内存持久映射：context_id -> SharedContext
        self._contexts: Dict[str, SharedContext] = {}
        # docs base path for simple fragment loading
        self._docs_base = Path("docs") / "stories"

    async def load_context_for_agent(self, agent_id: str, task_type: Optional[str] = None) -> SharedContext:
        """
        异步加载用于 agent 的 SharedContext：
        - 先从 L1 缓存查找（key = f"{agent_id}:{task_type}"）
        - 缓存未命中时扫描 docs/stories，选择与 task_type 相关的文档片段（简单匹配）
        - 构建 SharedContext 并放入内存表与 L1 缓存
        """
        if task_type is None:
            task_type = "general"

        cache_key = f"{agent_id}:{task_type}"
        cached = self._l1.get(cache_key)
        if cached:
            logger.debug("ContextLoader L1 cache hit for key=%s", cache_key)
            # cached.content 可能是 SharedContext 或其 dict 表示
            content = cached.content
            if isinstance(content, SharedContext):
                content.touch()
                return content
            else:
                # 劫持 dict -> 尝试重建 SharedContext
                try:
                    d = dict(content)
                    sc = SharedContext(
                        context_id=d.get("context_id", str(uuid.uuid4())),
                        agent_id=d.get("agent_id", agent_id),
                        task_type=d.get("task_type", task_type),
                        document_fragments=d.get("document_fragments", {}),
                        metadata=d.get("metadata", {}),
                        version=int(d.get("version", 0)),
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow(),
                    )
                    sc.touch()
                    return sc
                except Exception:
                    # fallback: 继续重新生成
                    logger.exception("Failed to reconstruct SharedContext from cache for key=%s", cache_key)

        # 生成 SharedContext（IO 操作：读取 docs 文件 -> 可能耗时）
        fragments = {}
        selected_count = 0
        try:
            if self._docs_base.exists() and self._docs_base.is_dir():
                for f in sorted(self._docs_base.iterdir()):
                    if not f.is_file() or f.suffix.lower() != ".md":
                        continue
                    try:
                        text = f.read_text(encoding="utf-8")
                    except Exception:
                        logger.exception("Failed to read doc file: %s", f)
                        continue
                    # 简单选择逻辑：如果 task_type 在文件内则优先选取，否则当作降级候选
                    lowered = text.lower()
                    if task_type and task_type.lower() in lowered:
                        fragments[str(f)] = text[:200]  # 取前 200 字符作为片段
                        selected_count += 1
                    else:
                        # 仅在未找到任何匹配时作为通用片段使用（稍后可能被替换）
                        fragments.setdefault("fallback:" + str(f), text[:120])
            else:
                logger.debug("ContextLoader: docs/stories directory not found at %s", str(self._docs_base))
        except Exception as exc:
            logger.exception("Error while scanning docs for context: %s", exc)

        # Build SharedContext
        context_id = str(uuid.uuid4())
        sc = SharedContext(
            context_id=context_id,
            agent_id=agent_id,
            task_type=task_type,
            document_fragments=fragments,
            metadata={"selected_count": selected_count, "source": "docs/stories"},
            version=1,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        # store
        self._contexts[context_id] = sc
        # cache
        try:
            self._l1.put(cache_key, sc, expiry_seconds=300)
        except Exception:
            logger.exception("Failed to put SharedContext into L1 cache for key=%s", cache_key)

        return sc

    async def update_shared_context(self, context_id: str, updates: Dict[str, Any]) -> bool:
        """
        最小实现：合并 updates 到已存在的 SharedContext（如果存在），并更新版本与时间戳。
        返回 True 表示成功。
        """
        try:
            sc = self._contexts.get(context_id)
            if not sc:
                logger.warning("update_shared_context: context_id %s not found", context_id)
                return False
            # 允许更新 metadata 和 document_fragments
            md = updates.get("metadata")
            if isinstance(md, dict):
                sc.metadata.update(md)
            df = updates.get("document_fragments")
            if isinstance(df, dict):
                sc.document_fragments.update(df)
            sc.version = int(sc.version) + 1
            sc.updated_at = datetime.utcnow()
            sc.touch()
            return True
        except Exception as exc:
            logger.exception("update_shared_context failed for %s: %s", context_id, exc)
            return False

    async def sync_context_between_agents(self, from_agent: str, to_agent: str) -> bool:
        """
        最小实现：查找最近由 from_agent 创建或使用的 SharedContext 并复制一份给 to_agent。
        返回 True 表示同步成功（或没有可同步的内容也返回 True）。
        """
        try:
            # 查找最近使用的 context（按 updated_at）
            candidates = [c for c in self._contexts.values() if c.agent_id == from_agent]
            if not candidates:
                logger.debug("No contexts found for agent %s to sync", from_agent)
                return True
            latest = max(candidates, key=lambda c: c.updated_at)
            # 复制一个新的 SharedContext 为 to_agent
            new_id = str(uuid.uuid4())
            copied = SharedContext(
                context_id=new_id,
                agent_id=to_agent,
                task_type=latest.task_type,
                document_fragments=dict(latest.document_fragments),
                metadata=dict(latest.metadata),
                version=latest.version,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            self._contexts[new_id] = copied
            # also put into cache under to_agent key
            cache_key = f"{to_agent}:{copied.task_type or 'general'}"
            self._l1.put(cache_key, copied, expiry_seconds=300)
            logger.info("Synced context %s from %s to %s as %s", latest.context_id, from_agent, to_agent, new_id)
            return True
        except Exception as exc:
            logger.exception("sync_context_between_agents failed from %s to %s: %s", from_agent, to_agent, exc)
            return False

    def get_context_cache_stats(self) -> Dict[str, Any]:
        """返回当前 L1 缓存统计信息和已存 SharedContext 的基本计数。"""
        try:
            stats = self._l1.stats()
            stats["contexts_stored"] = len(self._contexts)
            stats["context_ids"] = list(self._contexts.keys())[:50]  # 限制输出大小
            return stats
        except Exception:
            logger.exception("Error getting context cache stats")
            return {}

    # 辅助同步方法（同步 API，但内部可能调用异步方法）
    def sync_context_between_agents_sync(self, from_agent: str, to_agent: str) -> bool:
        return asyncio.get_event_loop().run_until_complete(self.sync_context_between_agents(from_agent, to_agent))
