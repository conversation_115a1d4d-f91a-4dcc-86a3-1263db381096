"""
Structured logging configuration for the workflow system.

Provides:
- J<PERSON>NFormatter: lightweight JSON formatter for structured logs
- configure_logging: sets up console + rotating file handlers and optional asynchronous QueueHandler
- get_logger: convenience wrapper to get module loggers

Usage:
    from workflow.logging_config import configure_logging, get_logger
    configure_logging(log_file="logs/workflow.log", level=logging.INFO, use_queue=True)
    logger = get_logger(__name__)
"""
from __future__ import annotations

import json
import logging
import logging.handlers
import queue
from logging import LogRecord
from logging.handlers import <PERSON><PERSON>tingFileHand<PERSON>, Que<PERSON><PERSON><PERSON><PERSON>, QueueListener
from typing import Optional, Any, Dict

DEFAULT_LOG_FORMAT = "%(asctime)sZ %(levelname)s %(name)s %(message)s"


class JSONFormatter(logging.Formatter):
    """A minimal JSON formatter for structured logs."""

    def format(self, record: LogRecord) -> str:
        # Base record
        log_record: Dict[str, Any] = {
            "timestamp": self.formatTime(record) + "Z",
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }

        # Include exception info if present
        if record.exc_info:
            try:
                log_record["exc_info"] = self.formatException(record.exc_info)
            except Exception:
                log_record["exc_info"] = "failed to format exc_info"

        # Allow extra structured payloads via record.__dict__.get("extra") or record.__dict__.get("context")
        extra = getattr(record, "extra", None) or getattr(record, "context", None)
        if extra and isinstance(extra, dict):
            log_record["context"] = extra

        # Merge limited additional attributes if they are JSON-serializable
        avoid_keys = {
            "name", "msg", "args", "levelname", "levelno", "pathname",
            "filename", "module", "exc_info", "exc_text", "stack_info",
            "lineno", "funcName", "created", "msecs", "relativeCreated",
            "thread", "threadName", "processName", "process", "message"
        }
        for k, v in record.__dict__.items():
            if k in avoid_keys:
                continue
            # Only add simple JSON-serializable values
            try:
                json.dumps({k: v})
                log_record[k] = v
            except Exception:
                # skip non-serializable
                pass

        return json.dumps(log_record, ensure_ascii=False)


_default_queue: Optional[queue.Queue] = None
_default_listener: Optional[QueueListener] = None


def configure_logging(log_file: Optional[str] = None,
                      level: int = logging.INFO,
                      max_bytes: int = 10 * 1024 * 1024,
                      backup_count: int = 5,
                      use_queue: bool = True) -> None:
    """
    Configure structured logging for the application.

    - log_file: path to rotating log file (if None, file handler is not added)
    - level: logging level
    - max_bytes / backup_count: rotating file handler settings
    - use_queue: if True, enable QueueHandler + QueueListener for asynchronous logging
    """
    global _default_queue, _default_listener

    # Root logger config
    root_logger = logging.getLogger()
    root_logger.setLevel(level)

    formatter = JSONFormatter()

    handlers = []

    # Console handler (synchronous, JSON-formatted)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)

    # File handler (rotating)
    if log_file:
        file_handler = RotatingFileHandler(log_file, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8")
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)

    if use_queue:
        # Set up a queue and listener to handle log records in a background thread
        q: queue.Queue = queue.Queue(-1)
        q_handler = QueueHandler(q)
        q_handler.setLevel(level)

        # Listener handlers should be the actual handlers that write logs
        listener = QueueListener(q, *handlers, respect_handler_level=True)
        listener.start()

        # Replace existing handlers on root with the QueueHandler
        root_logger.handlers = [q_handler]

        # Keep references for shutdown
        _default_queue = q
        _default_listener = listener
    else:
        # Directly attach handlers to root logger
        # Clear prior handlers to avoid duplicate logs in test runs
        root_logger.handlers = []
        for h in handlers:
            root_logger.addHandler(h)


def shutdown_logging() -> None:
    """Shut down the queue listener (if any) and flush handlers."""
    global _default_listener
    if _default_listener:
        try:
            _default_listener.stop()
        except Exception:
            pass
    logging.shutdown()


def get_logger(name: str) -> logging.Logger:
    """Convenience getter for module loggers."""
    return logging.getLogger(name)