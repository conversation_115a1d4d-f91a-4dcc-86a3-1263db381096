from dataclasses import dataclass, field, asdict
from datetime import datetime, timezone
from typing import Dict, Optional, Any, List
import json
import uuid

from enum import Enum
from typing import TypedDict, Union

@dataclass
class WorkflowState:
    """
    WorkflowState 数据模型
    用于跟踪自动化工作流的当前状态与进度。
    """

    workflow_id: str
    current_stage: str
    project_path: str
    documents_status: Dict[str, Any] = field(default_factory=dict)
    epic_progress: Dict[str, Any] = field(default_factory=dict)
    story_progress: Dict[str, Any] = field(default_factory=dict)
    created_at: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat() + "Z")
    updated_at: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat() + "Z")

    def __post_init__(self) -> None:
        # 基本校验
        if not isinstance(self.workflow_id, str) or not self.workflow_id:
            raise ValueError("workflow_id must be a non-empty string")
        if not isinstance(self.current_stage, str) or not self.current_stage:
            raise ValueError("current_stage must be a non-empty string")
        if not isinstance(self.project_path, str) or not self.project_path:
            raise ValueError("project_path must be a non-empty string")
        # ensure timestamps are strings
        if not isinstance(self.created_at, str):
            self.created_at = datetime.now(timezone.utc).isoformat() + "Z"
        if not isinstance(self.updated_at, str):
            self.updated_at = datetime.now(timezone.utc).isoformat() + "Z"

    @staticmethod
    def new(project_path: str, initial_stage: str = "document_processing") -> "WorkflowState":
        """
        创建一个新的 WorkflowState 实例，自动生成 workflow_id 和时间戳。
        """
        return WorkflowState(
            workflow_id=str(uuid.uuid4()),
            current_stage=initial_stage,
            project_path=project_path,
        )

    def touch(self) -> None:
        """更新 updated_at 时间戳"""
        self.updated_at = datetime.now(timezone.utc).isoformat() + "Z"

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典，便于存储为 JSON/string 等"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "WorkflowState":
        """从字典反序列化为 WorkflowState，兼容 JSON 存储"""
        # Defensive copy and basic normalization
        d = dict(data)
        # Ensure required fields exist
        required = ["workflow_id", "current_stage", "project_path"]
        for k in required:
            if k not in d:
                raise ValueError(f"Missing required field '{k}' for WorkflowState")
        # Parse JSON strings if some fields were stored as JSON strings
        for key in ("documents_status", "epic_progress", "story_progress"):
            if key in d and isinstance(d[key], str):
                try:
                    d[key] = json.loads(d[key])
                except Exception:
                    d[key] = {}
        return cls(
            workflow_id=d["workflow_id"],
            current_stage=d["current_stage"],
            project_path=d["project_path"],
            documents_status=d.get("documents_status", {}),
            epic_progress=d.get("epic_progress", {}),
            story_progress=d.get("story_progress", {}),
            created_at=d.get("created_at", datetime.now(timezone.utc).isoformat() + "Z"),
            updated_at=d.get("updated_at", datetime.now(timezone.utc).isoformat() + "Z"),
        )


@dataclass
class AgentStatus(Enum):
    ACTIVE = "active"
    IDLE = "idle"
    BUSY = "busy"
    COMPLETED = "completed"
    ERROR = "error"

@dataclass
class AgentSession:
    """Represents a short-lived session for an agent.

    Fields closely follow the Story 2.1 data model requirements.
    Status uses AgentStatus enum for stricter typing.
    """

    session_id: str
    agent_id: str
    status: AgentStatus  # use AgentStatus enum
    current_task: Optional[str]
    context: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_activity: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    performance_metrics: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self) -> None:
        # normalize status if a raw string was provided
        if isinstance(self.status, str):
            try:
                self.status = AgentStatus(self.status)
            except ValueError:
                raise ValueError(f"Invalid AgentSession status: {self.status}")

        # Basic validation
        if not isinstance(self.session_id, str) or not self.session_id:
            raise ValueError("session_id must be a non-empty string")
        if not isinstance(self.agent_id, str) or not self.agent_id:
            raise ValueError("agent_id must be a non-empty string")
        if not isinstance(self.context, dict):
            raise ValueError("context must be a dict")

    def touch(self) -> None:
        """Update last_activity timestamp."""
        self.last_activity = datetime.now(timezone.utc)

    def to_dict(self) -> Dict[str, Any]:
        d = {
            "session_id": self.session_id,
            "agent_id": self.agent_id,
            "status": self.status.value if isinstance(self.status, AgentStatus) else str(self.status),
            "current_task": self.current_task,
            "context": self.context,
            "created_at": self.created_at.isoformat() + "Z",
            "last_activity": self.last_activity.isoformat() + "Z",
            "performance_metrics": self.performance_metrics,
        }
        return d


@dataclass
class TaskExecution:
    """Represents execution metadata for a single task."""

    task_id: str
    story_id: Optional[str]
    agent_id: Optional[str]
    task_type: str
    status: str  # pending, running, completed, failed
    input_context: Dict[str, Any] = field(default_factory=dict)
    output_result: Dict[str, Any] = field(default_factory=dict)
    error_message: Optional[str] = None
    execution_time: Optional[float] = None
    created_at: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat() + "Z")
    session_id: Optional[str] = None
    transfer_chain: list = field(default_factory=list)
    notification_sent: bool = False

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class CodeGenerationResult:
    """代码生成结果数据模型"""
    success: bool
    generated_files: List[str]  # 生成的文件路径列表
    modified_files: List[str]   # 修改的文件路径列表
    deleted_files: List[str]    # 删除的文件路径列表
    operation_type: str         # create, modify, delete, refactor
    quality_metrics: Dict[str, Any]  # 代码质量指标
    validation_results: Dict[str, Any]  # 验证结果
    errors: List[str]           # 错误信息
    warnings: List[str]         # 警告信息
    execution_time: float       # 执行时间
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        d = asdict(self)
        d['created_at'] = self.created_at.isoformat() + "Z"
        return d

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CodeGenerationResult":
        """从字典反序列化"""
        d = dict(data)
        if 'created_at' in d and isinstance(d['created_at'], str):
            d['created_at'] = datetime.fromisoformat(d['created_at'].replace('Z', '+00:00'))
        return cls(**d)

    def to_json(self) -> str:
        """序列化为 JSON 字符串"""
        import json
        return json.dumps(self.to_dict(), indent=2)

    @classmethod
    def from_json(cls, json_str: str) -> "CodeGenerationResult":
        """从 JSON 字符串反序列化"""
        import json
        data = json.loads(json_str)
        return cls.from_dict(data)

    def save_to_file(self, file_path: str) -> None:
        """保存到文件"""
        from pathlib import Path
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            f.write(self.to_json())

    @classmethod
    def load_from_file(cls, file_path: str) -> "CodeGenerationResult":
        """从文件加载"""
        from pathlib import Path
        path = Path(file_path)
        with open(path, 'r', encoding='utf-8') as f:
            return cls.from_json(f.read())


@dataclass
class FileSpec:
    """文件规格数据模型"""
    path: str                   # 文件路径
    content: str               # 文件内容
    language: str              # 编程语言
    template: Optional[str]    # 使用的模板名称
    parameters: Dict[str, Any] # 模板参数
    operation: str             # create, modify, delete

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return asdict(self)


@dataclass
class SharedContext:
    """共享上下文数据模型，用于在智能体间共享加载的文档片段和元数据。"""
    context_id: str
    agent_id: str
    task_type: Optional[str]
    document_fragments: Dict[str, str]  # 文档路径 -> 内容片段
    metadata: Dict[str, Any]
    version: int
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    access_count: int = 0
    last_accessed: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def touch(self) -> None:
        self.access_count += 1
        self.last_accessed = datetime.now(timezone.utc)

    def to_dict(self) -> Dict[str, Any]:
        d = asdict(self)
        d['created_at'] = self.created_at.isoformat() + "Z"
        d['updated_at'] = self.updated_at.isoformat() + "Z"
        d['last_accessed'] = self.last_accessed.isoformat() + "Z"
        return d


@dataclass
class ContextCache:
    """缓存条目数据模型，用于 L1/L2 缓存管理"""
    cache_key: str
    content: Any
    size_bytes: int
    hit_count: int = 0
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    last_accessed: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    expiry_time: Optional[datetime] = None

    def touch(self) -> None:
        self.hit_count += 1
        self.last_accessed = datetime.now(timezone.utc)

# WorkflowExecution and UserInteraction models (from Story 4.1)
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from datetime import datetime

@dataclass
class WorkflowExecution:
    """
    Execution record for a long-running workflow (Story 4.1).
    Stores progress, checkpoints and user confirmations.
    """
    execution_id: str
    workflow_type: str          # greenfield, brownfield
    current_phase: str          # document_processing, epic_creation, story_development, implementation
    total_phases: int = 0
    completed_phases: int = 0
    phase_progress: Dict[str, float] = field(default_factory=dict)  # phase -> progress 0.0-1.0
    user_confirmations: List[Dict[str, Optional[str]]] = field(default_factory=list)  # records of user decisions
    checkpoints: List[Dict[str, Any]] = field(default_factory=list)           # generic checkpoint records
    status: str = "running"       # running, paused, completed, failed
    started_at: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat() + "Z")
    estimated_completion: Optional[str] = None
    actual_completion: Optional[str] = None

    def touch(self) -> None:
        """Update metadata timestamps if needed (keeps compat with other models)."""
        self.started_at = self.started_at or (datetime.now(timezone.utc).isoformat() + "Z")

    def to_dict(self) -> Dict[str, Any]:
        d = asdict(self)
        # ensure timestamps are strings
        if isinstance(self.started_at, datetime):
            d["started_at"] = self.started_at.isoformat() + "Z"
        return d

@dataclass
class UserInteraction:
    """
    Records a user interaction / confirmation during the workflow.
    """
    interaction_id: str
    phase: str
    message: str
    options: List[str] = field(default_factory=list)
    user_choice: Optional[str] = None
    timestamp: str = field(default_factory=lambda: datetime.now(timezone.utc).isoformat() + "Z")
    auto_proceed: bool = False

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)
@dataclass
class ErrorRecord:
    """
    错误记录数据模型（用于 Story 4.2）
    """
    error_id: str
    error_type: str             # temporary, permanent, user_error, system_error
    severity: str               # low, medium, high, critical
    phase: str                  # 发生错误的工作流程阶段
    component: str              # 发生错误的组件
    error_message: str          # 错误消息
    stack_trace: Optional[str] = None            # 堆栈跟踪
    context: Dict[str, Any] = field(default_factory=dict)     # 错误上下文信息
    retry_count: int = 0            # 重试次数
    recovery_suggestions: List[str] = field(default_factory=list)  # 恢复建议
    occurred_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    resolved_at: Optional[datetime] = None
    resolution_method: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        d = asdict(self)
        d["occurred_at"] = self.occurred_at.isoformat() + "Z"
        if self.resolved_at:
            d["resolved_at"] = self.resolved_at.isoformat() + "Z"
        return d

    @classmethod
    def new(cls, *, error_type: str, severity: str, phase: str, component: str, error_message: str, context: Optional[Dict[str, Any]] = None) -> "ErrorRecord":
        return cls(
            error_id=str(uuid.uuid4()),
            error_type=error_type,
            severity=severity,
            phase=phase,
            component=component,
            error_message=error_message,
            context=context or {},
        )

@dataclass
class RetryPolicy:
    """
    重试策略数据模型（用于 Story 4.2）
    """
    max_retries: int = 3            # 最大重试次数
    base_delay: float = 1.0         # 基础延迟时间（秒）
    max_delay: float = 60.0         # 最大延迟时间（秒）
    backoff_multiplier: float = 2.0 # 退避乘数
    retry_conditions: List[str] = field(default_factory=lambda: ["temporary"]) # 重试条件（基于 ErrorRecord.error_type）
    timeout: Optional[float] = None  # 超时时间（总重试窗口，秒）

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

    def compute_delay(self, retry_count: int) -> float:
        """计算根据指数退避的延迟（秒）"""
        delay = min(self.base_delay * (self.backoff_multiplier ** retry_count), self.max_delay)
        return float(delay)


@dataclass
class TestGenerationResult:
    """测试生成结果数据模型"""
    success: bool
    test_files: List[str]               # 生成的测试文件路径
    coverage_report: Dict[str, Any]     # 覆盖率报告
    test_count: int                     # 生成的测试用例数量
    quality_score: float                # 测试质量评分
    execution_results: Dict[str, Any]   # 测试执行结果
    errors: List[str]                   # 错误信息
    warnings: List[str]                 # 警告信息
    created_at: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        d = asdict(self)
        d['created_at'] = self.created_at.isoformat() + "Z"
        return d


@dataclass
class TestCase:
    """测试用例数据模型"""
    name: str                           # 测试用例名称
    target_function: str                # 目标函数名
    test_type: str                     # unit, integration, edge_case
    arrange_code: str                  # 准备阶段代码
    act_code: str                      # 执行阶段代码
    assert_code: str                   # 断言阶段代码
    mock_requirements: List[str]       # 需要的 Mock 对象
    fixtures: List[str]                # 需要的 Fixture

    def to_dict(self) -> Dict[str, Any]:
        """序列化为字典"""
        return asdict(self)



@dataclass
class StorySummary:
    """轻量 Story 模型，用于 Epic 下的条目列表"""
    story_id: str
    title: str

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Story:
    """完整的 Story 数据模型（用于 Epic 内）"""
    story_id: str                 # e.g., "1.1"
    title: str
    user_story: str               # "As a ... I want ... so that ..."
    acceptance_criteria: List[str] = field(default_factory=list)
    priority: Optional[str] = None
    estimated_days: Optional[str] = None
    dependencies: List[str] = field(default_factory=list)
    status: Optional[str] = None

    def __post_init__(self) -> None:
        if not isinstance(self.story_id, str) or not self.story_id:
            raise ValueError("story_id must be a non-empty string")
        if not isinstance(self.title, str):
            raise ValueError("title must be a string")
        if not isinstance(self.acceptance_criteria, list):
            raise ValueError("acceptance_criteria must be a list")

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class Epic:
    """Epic 数据模型"""
    epic_id: str                  # e.g., "1"
    title: str
    description: str = ""
    stories: List[Story] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)  # 依赖的其他 Epic ID
    priority: Optional[str] = None
    estimated_days: Optional[str] = None
    status: Optional[str] = None

    def __post_init__(self) -> None:
        if not isinstance(self.epic_id, str) or not self.epic_id:
            raise ValueError("epic_id must be a non-empty string")
        if not isinstance(self.title, str):
            raise ValueError("title must be a string")
        if not isinstance(self.stories, list):
            raise ValueError("stories must be a list of Story objects")

    def to_dict(self) -> Dict[str, Any]:
        d = asdict(self)
        # ensure stories are serialized properly
        d["stories"] = [s.to_dict() if hasattr(s, "to_dict") else s for s in self.stories]
        return d
