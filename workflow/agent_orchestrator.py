import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Optional, Any, List, Callable

from workflow.models import AgentSession
from workflow.models import TaskExecution
from workflow.context_loader import ContextLoader
import time
import json

logger = logging.getLogger(__name__)


class AgentOrchestrator:
    """Agent orchestrator to manage agent sessions, task assignment and event-driven communication."""

    def __init__(self) -> None:
        self._sessions: Dict[str, AgentSession] = {}
        self._lock = asyncio.Lock()
        # session timeout in seconds
        self._session_timeout = 60 * 30  # 30 minutes
        # event-driven communication
        self._event_handlers: Dict[str, List[Callable[[Dict[str, Any]], None]]] = {}
        self._task_queue: asyncio.Queue = asyncio.Queue()
        # context loader for automatic context injection (can be replaced with real index)
        self._context_loader: ContextLoader = ContextLoader()
        
    def subscribe_to_event(self, event_type: str, handler: Callable[[Dict[str, Any]], None]) -> None:
        """Subscribe a handler to an event type for event-driven communication."""
        if event_type not in self._event_handlers:
            self._event_handlers[event_type] = []
        self._event_handlers[event_type].append(handler)
        logger.info("Subscribed handler to event type: %s", event_type)
    
    async def emit_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """Emit an event to all subscribed handlers."""
        handlers = self._event_handlers.get(event_type, [])
        for handler in handlers:
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(data)
                else:
                    handler(data)
            except Exception as exc:
                logger.exception("Event handler error for %s: %s", event_type, exc)
        if handlers:
            logger.info("Emitted event %s to %d handlers", event_type, len(handlers))

    async def activate_agent(self, agent_id: str, context: Optional[Dict[str, Any]] = None) -> AgentSession:
        """Create and activate an AgentSession for the given agent_id."""
        async with self._lock:
            session_id = str(uuid.uuid4())
            session = AgentSession(
                session_id=session_id,
                agent_id=agent_id,
                status="active",
                current_task=None,
                context=context or {},
            )
            self._sessions[session_id] = session
            logger.info("Activated agent session: %s for agent %s", session_id, agent_id)
            # emit activation event
            await self.emit_event("agent_activated", {
                "session_id": session_id,
                "agent_id": agent_id,
                "context": context or {}
            })
            # schedule cleanup
            asyncio.create_task(self._session_watchdog(session_id))
            # asynchronously load and inject shared context without blocking activation
            try:
                asyncio.create_task(self._async_inject_context(session_id, agent_id, context or {}))
            except Exception:
                logger.exception("Failed to schedule async context injection for session %s", session_id)
            return session

    async def _async_inject_context(self, session_id: str, agent_id: str, context: Dict[str, Any]) -> None:
        """Background task to load context for an activated session and inject it."""
        try:
            task_type = None
            if isinstance(context, dict):
                task_type = context.get("task_type")
            if not task_type:
                task_type = "general"

            shared_context = await self._context_loader.load_context_for_agent(agent_id, task_type)

            # inject into session safely
            async with self._lock:
                session = self._sessions.get(session_id)
                if not session:
                    logger.warning("_async_inject_context: session %s not found", session_id)
                    return
                # store serializable representation
                try:
                    session.context["shared_context"] = shared_context.to_dict()
                except Exception:
                    # fallback: store minimal metadata
                    session.context["shared_context"] = {"context_id": getattr(shared_context, "context_id", None)}
                session.touch()

            # emit event that context is loaded
            await self.emit_event("context_loaded", {
                "session_id": session_id,
                "agent_id": agent_id,
                "context_id": getattr(shared_context, "context_id", None),
                "selected_count": shared_context.metadata.get("selected_count") if hasattr(shared_context, "metadata") else None,
            })

        except Exception as exc:
            logger.exception("Error in async context injection for session %s: %s", session_id, exc)

    async def get_agent_session(self, session_id: str) -> Optional[AgentSession]:
        return self._sessions.get(session_id)

    async def assign_task(self, agent_id: str, task: TaskExecution) -> TaskExecution:
        """Assign a task to an agent by creating/updating a session and returning TaskExecution.

        This implementation is intentionally simple: it activates a session if none exists in task.session_id,
        records the session_id on the task, appends to transfer_chain, and sets status to running.
        """
        async with self._lock:
            # ensure session exists
            session: Optional[AgentSession] = None
            if task.session_id:
                session = self._sessions.get(task.session_id)
            if not session:
                session = await self.activate_agent(agent_id, context=task.input_context)
                task.session_id = session.session_id
            # assign
            session.current_task = task.task_id
            session.status = "busy"
            session.touch()
            task.agent_id = agent_id
            task.status = "running"
            task.transfer_chain.append(f"assigned_to:{agent_id}")
            task_started = time.time()
            # record start time in output_result for later measurement
            task.output_result["_started_at_epoch"] = task_started
        logger.info("Assigned task %s to agent %s (session %s)", task.task_id, agent_id, task.session_id)
        # emit task assignment event
        await self.emit_event("task_assigned", {
            "task_id": task.task_id,
            "agent_id": agent_id,
            "session_id": task.session_id,
            "task_type": task.task_type
        })
        # Simulate non-blocking execution kickoff (real execution would be handled elsewhere)
        return task

    async def record_task_complete(self, task: TaskExecution, result: Optional[Dict[str, Any]] = None) -> TaskExecution:
        """Mark task as completed and record execution_time and transfer_chain entry."""
        async with self._lock:
            task.status = "completed"
            now = time.time()
            started = task.output_result.get("_started_at_epoch")
            if started:
                task.execution_time = now - started
            else:
                task.execution_time = 0.0
            if result:
                task.output_result.update(result)
            task.transfer_chain.append("completed")
            task.notification_sent = True
        logger.info("Task %s completed (agent=%s, exec_time=%.3f)", task.task_id, task.agent_id, task.execution_time)
        # emit task completion event
        await self.emit_event("task_completed", {
            "task_id": task.task_id,
            "agent_id": task.agent_id,
            "session_id": task.session_id,
            "execution_time": task.execution_time,
            "result": result
        })
        return task

    def task_transfer_chain_json(self, task: TaskExecution) -> str:
        """Return a JSON representation of the task transfer chain for visualization."""
        payload = {
            "task_id": task.task_id,
            "session_id": task.session_id,
            "agent_id": task.agent_id,
            "transfer_chain": task.transfer_chain,
            "created_at": task.created_at,
        }
        return json.dumps(payload)

    async def transfer_context(self, from_agent: str, to_agent: str, context: Dict[str, Any]) -> bool:
        """Transfer context between agents. Context must be sanitized before transfer.

        Implements:
        - input validation and sanitization (json-serializable subset)
        - basic idempotency via transfer_id in context
        - retry strategy for transient failures
        - optional merge into target session if present
        Returns True on success.
        """
        if not isinstance(context, dict):
            logger.error("transfer_context: context must be dict")
            return False

        # Ensure an idempotency key exists so repeated calls with same context are safe
        transfer_id = context.get("_transfer_id")
        if not transfer_id:
            transfer_id = str(uuid.uuid4())
            context["_transfer_id"] = transfer_id

        # Validate and sanitize context to JSON-serializable subset
        def sanitize(ctx: Dict[str, Any]) -> Dict[str, Any]:
            safe: Dict[str, Any] = {}
            for k, v in ctx.items():
                # Skip private/internal keys except _transfer_id
                if k.startswith("_") and k != "_transfer_id":
                    continue
                try:
                    json.dumps(v)
                    safe[k] = v
                except Exception:
                    # best-effort: convert common non-serializables to str
                    try:
                        safe[k] = str(v)
                    except Exception:
                        continue
            return safe

        sanitized_context = sanitize(context)

        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                # Merge into target agent's active session if available
                async with self._lock:
                    target_sessions = [s for s in self._sessions.values() if s.agent_id == to_agent]
                    if target_sessions:
                        # choose the latest session
                        target = max(target_sessions, key=lambda s: s.last_activity)
                        # avoid duplicate merges using transfer_id
                        merged = target.context.get("_merged_transfers", set())
                        if transfer_id in merged:
                            logger.info("transfer_context: transfer_id %s already merged into session %s", transfer_id, target.session_id)
                        else:
                            # record merge idempotently
                            if "_merged_transfers" not in target.context:
                                target.context["_merged_transfers"] = []
                            target.context.update(sanitized_context)
                            target.context["_merged_transfers"].append(transfer_id)
                            target.touch()
                            logger.info("Merged context into session %s for agent %s (keys=%s)", target.session_id, to_agent, list(sanitized_context.keys()))
                # emit context transfer event once merge succeeds
                await self.emit_event("context_transferred", {
                    "from_agent": from_agent,
                    "to_agent": to_agent,
                    "context_keys": list(sanitized_context.keys()),
                    "transfer_id": transfer_id,
                })
                return True
            except Exception as exc:
                logger.exception("transfer_context attempt %d failed from %s to %s: %s", attempt, from_agent, to_agent, exc)
                # exponential backoff
                await asyncio.sleep(0.2 * (2 ** (attempt - 1)))
                continue

        logger.error("transfer_context: all %d attempts failed for transfer_id %s", max_retries, transfer_id)
        return False

    async def get_agent_status(self, agent_id: str) -> Dict[str, Any]:
        """Get status information for a specific agent."""
        agent_sessions = [s for s in self._sessions.values() if s.agent_id == agent_id]
        if not agent_sessions:
            return {"agent_id": agent_id, "status": "inactive", "sessions": 0}
        
        active_sessions = [s for s in agent_sessions if s.status in ["active", "busy"]]
        return {
            "agent_id": agent_id,
            "status": "active" if active_sessions else "idle",
            "sessions": len(agent_sessions),
            "active_sessions": len(active_sessions),
            "current_tasks": [s.current_task for s in active_sessions if s.current_task],
            "last_activity": max(s.last_activity for s in agent_sessions).isoformat() if agent_sessions else None
        }

    async def health_check_agent(self, agent_id: str) -> bool:
        """Perform health check on an agent."""
        try:
            status = await self.get_agent_status(agent_id)
            is_healthy = status["status"] != "inactive"
            logger.info("Health check for agent %s: %s", agent_id, "healthy" if is_healthy else "unhealthy")
            return is_healthy
        except Exception as exc:
            logger.exception("Health check failed for agent %s: %s", agent_id, exc)
            return False

    async def get_agent_load_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get load metrics for all agents for load balancing."""
        metrics = {}
        agent_ids = set(s.agent_id for s in self._sessions.values())
        
        for agent_id in agent_ids:
            status = await self.get_agent_status(agent_id)
            metrics[agent_id] = {
                "active_sessions": status.get("active_sessions", 0),
                "total_sessions": status.get("sessions", 0),
                "load_score": status.get("active_sessions", 0) * 1.0  # Simple load scoring
            }
        return metrics

    async def get_least_loaded_agent(self, agent_ids: List[str]) -> Optional[str]:
        """Get the least loaded agent from a list of candidates for load balancing."""
        if not agent_ids:
            return None
            
        metrics = await self.get_agent_load_metrics()
        available_agents = [(agent_id, metrics.get(agent_id, {"load_score": 0})["load_score"]) 
                           for agent_id in agent_ids]
        available_agents.sort(key=lambda x: x[1])  # Sort by load score
        return available_agents[0][0] if available_agents else None

    async def generate_agent_status_report(self) -> Dict[str, Any]:
        """Generate comprehensive status report for all agents."""
        report = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "total_sessions": len(self._sessions),
            "agents": {},
            "system_health": "healthy"
        }
        
        agent_ids = set(s.agent_id for s in self._sessions.values())
        for agent_id in agent_ids:
            status = await self.get_agent_status(agent_id)
            health = await self.health_check_agent(agent_id)
            report["agents"][agent_id] = {**status, "health": health}
            if not health:
                report["system_health"] = "degraded"
        
        logger.info("Generated agent status report: %d agents, %d sessions", len(agent_ids), len(self._sessions))
        return report

    # Task 5: Workflow automation with observer pattern and state machine
    async def setup_workflow_automation(self) -> None:
        """Setup observer pattern handlers for automatic workflow progression."""
        # SM completes story -> notify Dev agent
        self.subscribe_to_event("story_completed", self._handle_story_completion)
        # Dev completes development -> notify QA agent  
        self.subscribe_to_event("development_completed", self._handle_development_completion)
        # QA completes review -> decide next step
        self.subscribe_to_event("qa_completed", self._handle_qa_completion)
        logger.info("Workflow automation handlers registered")

    async def _handle_story_completion(self, event_data: Dict[str, Any]) -> None:
        """Handle story completion event by activating Dev agent."""
        story_id = event_data.get("story_id")
        context = event_data.get("context", {})
        
        try:
            # Create task execution for development
            dev_task = TaskExecution(
                task_id=f"dev_{story_id}_{uuid.uuid4().hex[:8]}",
                story_id=story_id,
                agent_id=None,  # Will be set by assign_task
                task_type="development",
                status="pending",
                input_context=context
            )
            
            # Assign to dev agent
            await self.assign_task("dev", dev_task)
            logger.info("Auto-assigned development task for story %s", story_id)
            
        except Exception as exc:
            logger.exception("Failed to handle story completion for %s: %s", story_id, exc)

    async def _handle_development_completion(self, event_data: Dict[str, Any]) -> None:
        """Handle development completion event by activating QA agent."""
        story_id = event_data.get("story_id") 
        context = event_data.get("context", {})
        
        try:
            # Create task execution for QA review
            qa_task = TaskExecution(
                task_id=f"qa_{story_id}_{uuid.uuid4().hex[:8]}",
                story_id=story_id,
                agent_id=None,
                task_type="qa_review",
                status="pending", 
                input_context=context
            )
            
            # Assign to QA agent
            await self.assign_task("qa", qa_task)
            logger.info("Auto-assigned QA review task for story %s", story_id)
            
        except Exception as exc:
            logger.exception("Failed to handle development completion for %s: %s", story_id, exc)

    async def _handle_qa_completion(self, event_data: Dict[str, Any]) -> None:
        """Handle QA completion event by deciding next step based on results."""
        story_id = event_data.get("story_id")
        qa_result = event_data.get("result", {})
        qa_status = qa_result.get("status", "unknown")
        
        try:
            if qa_status == "approved":
                # Mark story as completed
                await self.emit_event("workflow_completed", {
                    "story_id": story_id,
                    "final_status": "completed"
                })
                logger.info("Story %s workflow completed successfully", story_id)
                
            elif qa_status == "rejected":
                # Return to development with feedback
                feedback_context = qa_result.get("feedback", {})
                
                dev_rework_task = TaskExecution(
                    task_id=f"dev_rework_{story_id}_{uuid.uuid4().hex[:8]}",
                    story_id=story_id,
                    agent_id=None,
                    task_type="development_rework",
                    status="pending",
                    input_context={"feedback": feedback_context, "original_context": event_data.get("context", {})}
                )
                
                await self.assign_task("dev", dev_rework_task)
                logger.info("Auto-assigned development rework task for story %s", story_id)
                
        except Exception as exc:
            logger.exception("Failed to handle QA completion for %s: %s", story_id, exc)

    async def trigger_workflow(self, story_id: str, context: Dict[str, Any]) -> None:
        """Manually trigger a workflow for a story."""
        await self.emit_event("story_completed", {
            "story_id": story_id, 
            "context": context
        })
        logger.info("Manually triggered workflow for story %s", story_id)

    async def _session_watchdog(self, session_id: str) -> None:
        """Background watchdog to clean up idle/expired sessions."""
        try:
            await asyncio.sleep(self._session_timeout)
            session = self._sessions.get(session_id)
            if not session:
                return
            now = datetime.utcnow()
            idle_seconds = (now - session.last_activity).total_seconds()
            if idle_seconds >= self._session_timeout:
                # cleanup
                async with self._lock:
                    session.status = "completed"
                    del self._sessions[session_id]
                    logger.info("Session %s timed out and was cleaned up", session_id)
        except Exception as exc:
            logger.exception("Session watchdog error for %s: %s", session_id, exc)


__all__ = ["AgentOrchestrator", "AgentSession"]


