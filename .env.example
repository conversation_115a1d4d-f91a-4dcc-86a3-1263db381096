# BMAD Agent FastMCP Service 环境变量配置模板
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# LLM 模式配置
# =============================================================================

# 使用内置 LLM 模式（推荐）
# true = 使用 Cursor 内置 LLM（默认，无需 API 费用）
# false = 使用外部 DeepSeek API（需要 API Key）
USE_BUILTIN_LLM=true

# DeepSeek API 配置（仅在 USE_BUILTIN_LLM=false 时需要）
# 获取 API Key: https://platform.deepseek.com/
# DEEPSEEK_API_KEY=your_deepseek_api_key_here

# =============================================================================
# 系统配置
# =============================================================================

# Python 字符编码（建议保持默认）
PYTHONIOENCODING=utf-8

# 日志级别（可选：DEBUG, INFO, WARNING, ERROR）
LOG_LEVEL=INFO

# =============================================================================
# 高级配置（通常不需要修改）
# =============================================================================

# BMAD 核心数据目录（相对路径）
BMAD_CORE_PATH=.bmad-core

# MCP 服务端口（如果需要网络模式）
# MCP_PORT=8000

# 最大并发请求数
# MAX_CONCURRENT_REQUESTS=10

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件为 .env:
#    cp .env.example .env
#
# 2. 根据需要修改配置
#
# 3. 重启服务使配置生效
#
# 4. 模式切换:
#    - 内置模式：USE_BUILTIN_LLM=true（推荐，无需 API）
#    - 外部模式：USE_BUILTIN_LLM=false + 设置 DEEPSEEK_API_KEY
#
# 5. 也可以通过 MCP 工具动态切换：
#    switch_llm_mode('builtin')   # 切换到内置模式
#    switch_llm_mode('external')  # 切换到外部模式