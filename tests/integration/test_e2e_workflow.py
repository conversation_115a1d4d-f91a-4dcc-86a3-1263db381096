import tempfile
import shutil
from pathlib import Path
import json

# Import the MCP service module
import bmad_agent_mcp as mcp_service

def setup_minimal_docs(root: Path):
    """
    Create a minimal docs/ structure with core docs, prd/ and architecture/ shards,
    and one epic/story to exercise the scan and detection logic.
    """
    docs = root / "docs"
    docs.mkdir()
    # core docs
    (docs / "brief.md").write_text("# brief\n\nMinimal brief")
    (docs / "prd.md").write_text("# prd\n\nMinimal prd")
    (docs / "architecture.md").write_text("# architecture\n\nMinimal architecture")
    # prd shards
    prd_dir = docs / "prd"
    prd_dir.mkdir()
    (prd_dir / "1_overview.md").write_text("# PRD Shard\n")
    # architecture shards
    arch_dir = docs / "architecture"
    arch_dir.mkdir()
    (arch_dir / "design.md").write_text("# Arch Shard\n")
    # epics and stories
    epics_dir = docs / "epics"
    epics_dir.mkdir()
    (epics_dir / "epic-1.md").write_text("# Epic 1\n")
    stories_dir = docs / "stories"
    stories_dir.mkdir()
    (stories_dir / "1.1.story.md").write_text("# Story 1.1\n")

def test_start_and_scan_workflow():
    # Create a temporary project dir
    tmpdir = Path(tempfile.mkdtemp(prefix="bmad_e2e_"))
    try:
        setup_minimal_docs(tmpdir)

        # Ensure there is a workflow definition available in bmad_core for start_workflow
        # Create a minimal WorkflowInfo instance compatible with BMADCore.parse_workflow_file outcome
        minimal_workflow = mcp_service.WorkflowInfo(
            id="test-workflow",
            name="Test Workflow",
            description="A minimal test workflow",
            type="test",
            project_types=[],
            sequence=[{"name": "step1"}, {"name": "step2"}]
        )
        # Inject into bmad_core
        mcp_service.bmad_core.workflows["test-workflow"] = minimal_workflow

        # Start the workflow
        start_res = mcp_service.start_workflow("test-workflow")
        assert start_res.get("success") is True, f"start_workflow failed: {start_res}"
 
        # Call the scanning/report tool against the temp project
        # Call the wrapped function directly
        if hasattr(mcp_service.get_workflow_state_report, '__wrapped__'):
            report_func = mcp_service.get_workflow_state_report.__wrapped__
        else:
            # Fallback: import and call the function directly 
            from workflow.state_engine import scan_project_structure, generate_status_report
            scan_result = scan_project_structure(str(tmpdir))
            workflow_state = {"current_stage": None, "workflow_id": None}
            status_report = generate_status_report(workflow_state, scan_result)
            report_res = {
                "success": True,
                "project_path": str(tmpdir),
                "scan_result": scan_result,
                "status_report": status_report
            }
        
        if hasattr(mcp_service.get_workflow_state_report, '__wrapped__'):
            report_res = report_func(str(tmpdir))
        assert report_res.get("success") is True, f"get_workflow_state_report failed: {report_res}"

        status_report = report_res.get("status_report", {})
        # Expect detected stage to be at least document_processing or later
        detected_stage = status_report.get("detected_stage")
        assert detected_stage in ("document_processing", "document_validated", "epic_creation", "story_development", "ready_for_implementation"), \
            f"Unexpected detected_stage: {detected_stage}"

        # Ensure core docs were detected
        core_docs = status_report.get("core_docs", {})
        assert core_docs.get("brief.md") is True
        assert core_docs.get("prd.md") is True
        assert core_docs.get("architecture.md") is True

        # Ensure counts are reported
        assert status_report.get("prd_shards_count", 0) >= 1
        assert status_report.get("architecture_shards_count", 0) >= 1
        assert status_report.get("epics_count", 0) >= 1
        assert status_report.get("stories_count", 0) >= 1

    finally:
        # cleanup
        shutil.rmtree(tmpdir)