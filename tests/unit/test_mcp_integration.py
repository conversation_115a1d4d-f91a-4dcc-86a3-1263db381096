"""
测试 MCP 集成功能
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock


class TestMCPIntegration:
    """测试 MCP 集成功能"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = Path(self.temp_dir)
        
        # 创建模板目录结构
        template_dir = self.project_root / "workflow" / "templates" / "python"
        template_dir.mkdir(parents=True)
        
        # 创建测试模板
        test_template = """class {{ class_name }}:
    \"\"\"{{ description }}\"\"\"
    
    def __init__(self):
        pass"""
        
        with open(template_dir / "class.j2", 'w') as f:
            f.write(test_template)
        
        # 创建测试配置
        test_config = """templates:
  class:
    description: "测试类模板"
    default_parameters:
      class_name: "TestClass"
      description: "测试类"
"""
        
        with open(template_dir / "templates.yaml", 'w') as f:
            f.write(test_config)
    
    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_generate_code_integration(self):
        """测试代码生成集成功能"""
        from workflow.code_generator import CodeGenerator
        from workflow.models import CodeGenerationResult

        # 创建真实的代码生成器
        generator = CodeGenerator(str(self.project_root))

        story_data = {"id": "test_story", "title": "Test Story"}
        task_data = {
            "name": "Create simple class",
            "description": "Create a simple Python class",
            "operation_type": "create"
        }

        # 模拟 _parse_task_requirements 方法
        with patch.object(generator, '_parse_task_requirements') as mock_parse:
            from workflow.models import FileSpec
            mock_parse.return_value = [
                FileSpec(
                    path="test_class.py",
                    content="class TestClass:\n    def __init__(self):\n        pass\n",
                    language="python",
                    template=None,
                    parameters={},
                    operation="create"
                )
            ]

            result = generator.generate_code(story_data, task_data)

            assert isinstance(result, CodeGenerationResult)
            assert result.success is True
            assert len(result.generated_files) == 1
            assert "test_class.py" in result.generated_files

            # 验证文件是否创建
            test_file = self.project_root / "test_class.py"
            assert test_file.exists()

            with open(test_file, 'r') as f:
                content = f.read()
            assert "class TestClass:" in content
    
    def test_template_manager_integration(self):
        """测试模板管理器集成功能"""
        from workflow.template_manager import TemplateManager

        template_root = self.project_root / "workflow" / "templates"
        manager = TemplateManager(str(template_root))

        # 测试获取可用模板
        templates = manager.get_available_templates("python")
        assert len(templates) > 0

        class_template = next((t for t in templates if t['name'] == 'class'), None)
        assert class_template is not None
        assert class_template['language'] == 'python'

        # 测试模板渲染
        parameters = {
            'class_name': 'MyTestClass',
            'description': '我的测试类'
        }

        content = manager.render_template('class', 'python', parameters)
        assert 'class MyTestClass:' in content
        assert '我的测试类' in content

    def test_code_structure_analysis_integration(self):
        """测试代码结构分析集成功能"""
        from workflow.code_generator import CodeGenerator

        # 创建测试文件
        test_file = "analysis_test.py"
        test_content = '''"""测试模块"""
import os
from typing import Dict

class TestClass:
    """测试类"""

    def __init__(self):
        pass

    def test_method(self, param: str) -> str:
        """测试方法"""
        return param

def test_function() -> None:
    """测试函数"""
    pass
'''

        full_path = self.project_root / test_file
        with open(full_path, 'w') as f:
            f.write(test_content)

        generator = CodeGenerator(str(self.project_root))
        analysis = generator.analyze_code_structure(test_file)

        assert 'classes' in analysis
        assert 'functions' in analysis
        assert 'imports' in analysis
        assert 'complexity' in analysis

        # 验证类分析
        assert len(analysis['classes']) == 1
        test_class = analysis['classes'][0]
        assert test_class['name'] == 'TestClass'
        assert len(test_class['methods']) == 2  # __init__ and test_method

        # 验证函数分析
        assert len(analysis['functions']) == 1
        func = analysis['functions'][0]
        assert func['name'] == 'test_function'

        # 验证导入分析
        assert len(analysis['imports']) >= 2  # os and typing.Dict

    def test_code_quality_validation_integration(self):
        """测试代码质量验证集成功能"""
        from workflow.code_generator import CodeGenerator

        # 创建有效的测试文件
        test_file = "quality_test.py"
        test_content = '''"""有效的测试文件"""
import logging

logger = logging.getLogger(__name__)


def valid_function(param: str) -> str:
    """有效的函数"""
    logger.info(f"Processing {param}")
    return param.upper()


class ValidClass:
    """有效的类"""

    def __init__(self, name: str) -> None:
        """初始化"""
        self.name = name

    def process(self) -> str:
        """处理方法"""
        return f"Processed: {self.name}"
'''

        full_path = self.project_root / test_file
        with open(full_path, 'w') as f:
            f.write(test_content)

        generator = CodeGenerator(str(self.project_root))

        # 测试语法验证
        results = generator._validate_code([test_file])

        assert 'syntax_valid' in results
        assert 'format_valid' in results
        assert 'lint_valid' in results
        assert 'custom_rules_valid' in results

        # 语法应该是有效的
        assert results['syntax_valid'] is True

        # 自定义规则应该通过（没有 print 语句）
        custom_results = generator.validate_custom_rules([test_file])
        assert custom_results['success'] is True
        assert len([v for v in custom_results['violations'] if 'print(' in v]) == 0
