"""
Unit tests for TestGenerator
Generated manually to validate the implementation
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
import tempfile
import os

from workflow.code_generator import TestGenerator
from workflow.models import TestGenerationResult, TestCase


class TestTestGenerator:
    """Test cases for TestGenerator"""

    @pytest.fixture
    def temp_project_root(self):
        """Create a temporary project root for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create some source files for testing
            source_file = Path(temp_dir) / "sample_module.py"
            source_file.write_text('''
"""Sample module for testing"""

class SampleClass:
    """A sample class"""
    
    def __init__(self):
        self.value = 0
    
    def public_method(self, arg1, arg2):
        """A public method"""
        return arg1 + arg2
    
    def _private_method(self):
        """A private method"""
        return "private"

def standalone_function(x, y):
    """A standalone function"""
    return x * y
''')
            yield temp_dir

    def test_init(self, temp_project_root):
        """Test TestGenerator initialization"""
        # Arrange & Act
        generator = TestGenerator(temp_project_root)

        # Assert
        assert generator.project_root == Path(temp_project_root)
        assert generator.code_generator is not None
        assert 'unit' in generator.test_output_dirs
        assert 'integration' in generator.test_output_dirs
        
        # Verify test directories are created
        assert generator.test_output_dirs['unit'].exists()
        assert generator.test_output_dirs['integration'].exists()

    def test_analyze_source_file(self, temp_project_root):
        """Test source file analysis"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        
        # Act
        analysis = generator._analyze_source_file("sample_module.py")
        
        # Assert
        assert 'classes' in analysis
        assert 'functions' in analysis
        assert len(analysis['classes']) == 1
        assert len(analysis['functions']) == 1
        assert analysis['classes'][0]['name'] == 'SampleClass'
        assert analysis['functions'][0]['name'] == 'standalone_function'

    def test_generate_test_cases(self, temp_project_root):
        """Test test case generation"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        analysis = {
            'classes': [{
                'name': 'SampleClass',
                'methods': [
                    {'name': 'public_method', 'args': ['self', 'arg1', 'arg2']},
                    {'name': '_private_method', 'args': ['self']}
                ]
            }],
            'functions': [
                {'name': 'standalone_function', 'args': ['x', 'y']}
            ]
        }
        
        # Act
        test_cases = generator._generate_test_cases("sample_module.py", analysis)
        
        # Assert
        assert len(test_cases) >= 2  # At least one for class method and one for function
        assert any(tc.target_function == 'SampleClass.public_method' for tc in test_cases)
        assert any(tc.target_function == 'standalone_function' for tc in test_cases)
        # Should not generate tests for private methods
        assert not any('_private_method' in tc.target_function for tc in test_cases)

    def test_generate_function_tests(self, temp_project_root):
        """Test function test generation"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        func_info = {'name': 'test_func', 'args': ['x', 'y']}
        
        # Act
        test_cases = generator._generate_function_tests("sample_module.py", func_info)
        
        # Assert
        assert len(test_cases) == 1
        test_case = test_cases[0]
        assert test_case.name == 'test_test_func_basic'
        assert test_case.target_function == 'test_func'
        assert test_case.test_type == 'unit'
        assert 'x = None' in test_case.arrange_code
        assert 'y = None' in test_case.arrange_code
        assert 'result = test_func(x, y)' in test_case.act_code
        assert 'assert result is not None' in test_case.assert_code

    def test_generate_method_tests(self, temp_project_root):
        """Test method test generation"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        method_info = {'name': 'test_method', 'args': ['self', 'param']}
        
        # Act
        test_cases = generator._generate_method_tests("sample_module.py", "TestClass", method_info)
        
        # Assert
        assert len(test_cases) == 1
        test_case = test_cases[0]
        assert test_case.name == 'test_testclass_test_method_basic'
        assert test_case.target_function == 'TestClass.test_method'
        assert 'instance = TestClass()' in test_case.arrange_code
        assert 'param = None' in test_case.arrange_code
        assert 'result = instance.test_method(param)' in test_case.act_code

    def test_create_test_file(self, temp_project_root):
        """Test test file creation"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        test_cases = [
            TestCase(
                name="test_sample_basic",
                target_function="sample_function",
                test_type="unit",
                arrange_code="# Arrange\nx = 1",
                act_code="# Act\nresult = sample_function(x)",
                assert_code="# Assert\nassert result is not None",
                mock_requirements=[],
                fixtures=[]
            )
        ]
        
        # Act
        result_path = generator._create_test_file("sample_module.py", test_cases)
        
        # Assert
        assert result_path is not None
        assert result_path.endswith('test_sample_module.py')
        
        # Verify file was created and has content
        full_path = Path(temp_project_root) / result_path
        assert full_path.exists()
        content = full_path.read_text()
        assert 'import pytest' in content
        assert 'from unittest.mock import Mock, patch' in content
        assert 'def test_sample_basic():' in content
        assert '# Arrange' in content
        assert '# Act' in content
        assert '# Assert' in content

    def test_calculate_quality_score(self, temp_project_root):
        """Test quality score calculation"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        test_cases = [
            TestCase("test1", "func1", "unit", "", "", "assert True", [], []),
            TestCase("test2", "func2", "unit", "", "", "assert False", [], [])
        ]
        errors = []
        
        # Act
        score = generator._calculate_quality_score(test_cases, errors)
        
        # Assert
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should be above base score due to having assertions

    def test_calculate_quality_score_with_errors(self, temp_project_root):
        """Test quality score with errors"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        test_cases = [TestCase("test1", "func1", "unit", "", "", "assert True", [], [])]
        errors = ["Error 1", "Error 2"]
        
        # Act
        score = generator._calculate_quality_score(test_cases, errors)
        
        # Assert
        assert score < 0.7  # Should be lower due to errors

    @patch('subprocess.run')
    def test_execute_tests_success(self, mock_run, temp_project_root):
        """Test successful test execution"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        mock_run.return_value = Mock(
            returncode=0,
            stdout="test_sample.py::test_function PASSED\n",
            stderr=""
        )
        
        # Act
        results = generator._execute_tests(['tests/unit/test_sample.py'])
        
        # Assert
        assert results['total_tests'] == 1
        assert results['passed'] == 1
        assert results['failed'] == 0
        assert len(results['errors']) == 0

    @patch('subprocess.run')
    def test_execute_tests_with_failures(self, mock_run, temp_project_root):
        """Test test execution with failures"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        mock_run.return_value = Mock(
            returncode=1,
            stdout="test_sample.py::test_function1 PASSED\ntest_sample.py::test_function2 FAILED\n",
            stderr="AssertionError: test failed"
        )
        
        # Act
        results = generator._execute_tests(['tests/unit/test_sample.py'])
        
        # Assert
        assert results['total_tests'] == 2
        assert results['passed'] == 1
        assert results['failed'] == 1
        assert len(results['errors']) > 0

    def test_generate_tests_integration(self, temp_project_root):
        """Integration test for the main generate_tests method"""
        # Arrange
        generator = TestGenerator(temp_project_root)
        
        # Act
        result = generator.generate_tests(['sample_module.py'])
        
        # Assert
        assert isinstance(result, TestGenerationResult)
        assert result.success is True or len(result.errors) > 0  # Either succeeds or has documented errors
        assert result.test_count > 0
        assert len(result.test_files) > 0
        assert 0.0 <= result.quality_score <= 1.0