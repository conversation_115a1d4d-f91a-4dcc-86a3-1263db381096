import json
from pathlib import Path
import shutil
import tempfile

from workflow.workflow_executor import start_workflow_executor

def test_interaction_persistence(tmp_path):
    # Prepare a minimal project layout with a docs/prd.md file
    proj_dir = tmp_path / "project"
    docs_dir = proj_dir / "docs"
    docs_dir.mkdir(parents=True, exist_ok=True)
    prd_file = docs_dir / "prd.md"
    prd_file.write_text("# PRD\n\nThis is a minimal PRD for testing.", encoding="utf-8")

    # interaction callback that records the received UserInteraction and returns 'continue'
    recorded = {}
    def interaction_callback(ui):
        # ui is expected to be a dataclass-like object with to_dict method or attributes
        try:
            recorded["interaction"] = ui.to_dict()
        except Exception:
            # fallback: attempt to introspect attributes
            recorded["interaction"] = {
                "interaction_id": getattr(ui, "interaction_id", None),
                "phase": getattr(ui, "phase", None),
                "message": getattr(ui, "message", None),
            }
        return "continue"

    # Run the executor
    summary = start_workflow_executor(str(proj_dir), auto_confirm=False, doc_filename="docs/prd.md", interaction_callback=interaction_callback)

    # Assert basic summary keys exist
    assert "scan" in summary
    assert "workflow_execution" in summary

    # Check that .workflow_state folder was created and contains at least one JSON state file
    state_dir = Path(proj_dir) / ".workflow_state"
    assert state_dir.exists() and state_dir.is_dir()

    state_files = list(state_dir.iterdir())
    # There should be at least one persisted state file
    assert len(state_files) >= 1

    # Try to load one of the state files and verify user_interactions recorded (if present)
    found = False
    for f in state_files:
        if f.suffix == ".json":
            try:
                content = json.loads(f.read_text(encoding="utf-8"))
                if "user_interactions" in content:
                    found = True
                    # user interaction should include an interaction_id recorded by callback
                    interactions = content.get("user_interactions") or []
                    # at least one interaction may be present (executor persists initial state and interaction)
                    assert isinstance(interactions, list)
            except Exception:
                continue
    assert found, "No persisted state file with 'user_interactions' found in .workflow_state"