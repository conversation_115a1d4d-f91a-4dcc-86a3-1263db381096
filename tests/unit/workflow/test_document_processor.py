import sys
from pathlib import Path
import shutil
import subprocess
import yaml
import os
import tempfile

import pytest

# Ensure repo root is importable
ROOT = Path(__file__).resolve().parents[4]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from workflow.document_processor import shard_documents, validate_document_structure, ShardingResult, extract_epics, create_epic_files

TEST_PRD = """# Title

## Section A
Content A

## Section B
Content B
"""

def write_core_config(tmpdir: Path, enable_exploder: bool):
    core = tmpdir / ".bmad-core"
    core.mkdir(exist_ok=True)
    cfg = {"markdownExploder": enable_exploder}
    (core / "core-config.yaml").write_text(yaml.safe_dump(cfg), encoding="utf-8")
    return core / "core-config.yaml"

def write_sample_prd(tmpdir: Path) -> Path:
    docs = tmpdir / "docs"
    docs.mkdir(exist_ok=True)
    prd = docs / "prd.md"
    prd.write_text(TEST_PRD, encoding="utf-8")
    return prd

def test_shard_documents_fallback_when_md_tree_missing(tmp_path, monkeypatch):
    """
    When md-tree is not in PATH or markdownExploder is false, shard_documents should
    fall back to built-in splitting and produce files under docs/prd/.
    """
    project_root = tmp_path
    # create repo layout and prd.md
    prd = write_sample_prd(project_root)
    # create .bmad-core config disabling markdownExploder
    write_core_config(project_root, enable_exploder=False)

    # Ensure md-tree is not found
    monkeypatch.setattr(shutil, "which", lambda name: None)

    # Run shard_documents
    res: ShardingResult = shard_documents(str(prd))

    assert res.success is True
    assert res.index_file is not None
    # check generated files list and that they exist
    assert any("intro.md" in p or "section-a.md" in p.lower() or "section-b.md" in p.lower() for p in res.sharded_files)
    for p in res.sharded_files:
        assert Path(p).exists()
        v = validate_document_structure(p)
        assert v.valid is True

def test_shard_documents_uses_md_tree_when_enabled_and_available(tmp_path, monkeypatch):
    """
    When markdownExploder is true and md-tree exists and returns success,
    shard_documents should return the ShardingResult produced by md-tree.
    We simulate md-tree by having it write expected files into the output dir.
    """
    project_root = tmp_path
    prd = write_sample_prd(project_root)
    cfg_path = write_core_config(project_root, enable_exploder=True)

    # Create a fake md-tree in a temporary bin dir and ensure it's used
    fake_bin = tmp_path / "bin"
    fake_bin.mkdir()
    fake_md_tree = fake_bin / "md-tree"
    # Create a dummy executable script that will simulate md-tree explode by creating files
    fake_md_tree.write_text("#!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\ninfile = Path(sys.argv[2])\noutdir = Path(sys.argv[3])\noutdir.mkdir(parents=True, exist_ok=True)\n# create two md files and an index\n(outdir / 'a.md').write_text('# A\\n\\ncontent')\n(outdir / 'b.md').write_text('# B\\n\\ncontent')\n(outdir / 'index.md').write_text('# Index\\n- [A](a.md)\\n- [B](b.md)')\n", encoding="utf-8")
    fake_md_tree.chmod(0o755)

    # monkeypatch PATH lookup to find our fake md-tree
    monkeypatch.setattr(shutil, "which", lambda name: str(fake_md_tree) if name == "md-tree" else None)

    # Also monkeypatch subprocess.run to call the real fake_md_tree script (it exists), so no need to mock run.
    # However ensure cwd is project root when calling shard_documents by running from that dir: shard_documents uses repo root detection.

    # Run shard_documents; since md-tree is "available" and config enables it, it should be used.
    res: ShardingResult = shard_documents(str(prd))

    assert res.success is True
    # md-tree should have created index.md in docs/prd/
    assert res.index_file is not None
    assert Path(res.index_file).exists()
    # there should be at least the two files we created
    assert any(p.endswith("a.md") or p.endswith("b.md") for p in res.sharded_files)

def test_validate_document_structure_detects_missing_file(tmp_path):
    p = tmp_path / "missing.md"
    res = validate_document_structure(str(p))
    assert res.valid is False
    assert any("not found" in issue.lower() for issue in res.issues)


# Epic extraction and creation tests (Story 1.3)

TEST_PRD_WITH_EPICS = """# Product Requirements Document

## Epic 1: User Authentication System
This epic covers the complete user authentication system including login, registration, and password management.

### Story 1.1: User Registration
**用户故事：** As a new user, I want to register an account so that I can access the system.

**验收标准：**
1. User can enter email and password
2. System validates email format
3. Password meets security requirements
4. Confirmation email is sent

### Story 1.2: User Login
**用户故事：** As a registered user, I want to log in so that I can access my account.

**验收标准：**
1. User can enter credentials
2. System validates credentials
3. User is redirected to dashboard

## Epic 2: Dashboard Management
This epic covers the user dashboard and basic navigation features.

### Story 2.1: Dashboard Overview
**用户故事：** As a logged-in user, I want to see a dashboard overview so that I can understand my current status.

**验收标准：**
1. Dashboard shows user information
2. Recent activity is displayed
3. Navigation menu is accessible
"""

def test_extract_epics_from_prd_shards(tmp_path):
    """Test Epic extraction from PRD shard files."""
    # Create test PRD shard files
    prd_dir = tmp_path / "docs" / "prd"
    prd_dir.mkdir(parents=True)

    # Write test PRD content to shard file
    shard_file = prd_dir / "user-features.md"
    shard_file.write_text(TEST_PRD_WITH_EPICS, encoding="utf-8")

    # Extract epics
    epics = extract_epics([str(shard_file)])

    # Verify extraction results
    assert len(epics) == 2

    # Check first epic
    epic1 = epics[0]
    assert epic1.epic_id == "1"
    assert "User Authentication System" in epic1.title
    assert "authentication system" in epic1.description.lower()
    assert len(epic1.stories) == 2

    # Check first story
    story1_1 = epic1.stories[0]
    assert story1_1.story_id == "1.1"
    assert "User Registration" in story1_1.title
    assert "new user" in story1_1.user_story.lower()
    assert len(story1_1.acceptance_criteria) == 4
    assert "email format" in story1_1.acceptance_criteria[1]

    # Check second epic
    epic2 = epics[1]
    assert epic2.epic_id == "2"
    assert "Dashboard Management" in epic2.title
    assert len(epic2.stories) == 1


def test_create_epic_files(tmp_path):
    """Test Epic file generation."""
    from workflow.models import Epic, Story

    # Create test Epic data
    story1 = Story(
        story_id="1.1",
        title="Test Story",
        user_story="As a user, I want to test so that I can verify functionality.",
        acceptance_criteria=["Criterion 1", "Criterion 2"],
        priority="高",
        estimated_days="2-3 天"
    )

    epic1 = Epic(
        epic_id="1",
        title="Test Epic",
        description="This is a test epic for validation.",
        stories=[story1],
        priority="高",
        estimated_days="5-7 天"
    )

    # Create Epic files
    out_dir = tmp_path / "epics"
    generated_files = create_epic_files([epic1], str(out_dir))

    # Verify file creation
    assert len(generated_files) == 1
    epic_file = Path(generated_files[0])
    assert epic_file.exists()
    assert epic_file.name == "epic-1.md"

    # Verify file content
    content = epic_file.read_text(encoding="utf-8")
    assert "# Epic 1: Test Epic" in content
    assert "## Epic 目标" in content
    assert "This is a test epic for validation." in content
    assert "## Story 列表" in content
    assert "### Story 1.1: Test Story" in content
    assert "**用户故事：** As a user, I want to test" in content
    assert "**验收标准：**" in content
    assert "1. Criterion 1" in content
    assert "2. Criterion 2" in content
    assert "**优先级：** 高" in content
    assert "**估算：** 2-3 天" in content

    # Verify index file creation
    index_file = out_dir / "index.md"
    assert index_file.exists()
    index_content = index_file.read_text(encoding="utf-8")
    assert "# Epics" in index_content
    assert "Epic 1: Test Epic" in index_content


def test_extract_epics_handles_empty_files(tmp_path):
    """Test Epic extraction handles empty or malformed files gracefully."""
    prd_dir = tmp_path / "docs" / "prd"
    prd_dir.mkdir(parents=True)

    # Create empty file
    empty_file = prd_dir / "empty.md"
    empty_file.write_text("", encoding="utf-8")

    # Create file with no epics
    no_epics_file = prd_dir / "no-epics.md"
    no_epics_file.write_text("# Some Document\n\nThis has no epics.", encoding="utf-8")

    # Extract epics
    epics = extract_epics([str(empty_file), str(no_epics_file)])

    # Should return empty list without errors
    assert len(epics) == 0


def test_extract_epics_handles_malformed_stories(tmp_path):
    """Test Epic extraction handles malformed story sections."""
    prd_dir = tmp_path / "docs" / "prd"
    prd_dir.mkdir(parents=True)

    malformed_content = """# PRD

## Epic 1: Test Epic
Description of epic.

### Story 1.1: Incomplete Story
This story has no proper user story format.

### Story 1.2: Another Story
**用户故事：** As a user, I want something.
No acceptance criteria here.

## Epic 2: Another Epic
### Story 2.1: Valid Story
**用户故事：** As a user, I want to do something so that I can achieve a goal.

**验收标准：**
1. First criterion
2. Second criterion
"""

    shard_file = prd_dir / "malformed.md"
    shard_file.write_text(malformed_content, encoding="utf-8")

    # Extract epics
    epics = extract_epics([str(shard_file)])

    # Should still extract epics, handling malformed sections gracefully
    assert len(epics) == 2

    epic1 = epics[0]
    assert epic1.epic_id == "1"
    assert len(epic1.stories) == 2

    # First story should have empty acceptance criteria
    story1_1 = epic1.stories[0]
    assert story1_1.story_id == "1.1"
    assert len(story1_1.acceptance_criteria) == 0

    # Second story should also have empty acceptance criteria
    story1_2 = epic1.stories[1]
    assert story1_2.story_id == "1.2"
    assert len(story1_2.acceptance_criteria) == 0

    # Second epic should have proper story
    epic2 = epics[1]
    assert epic2.epic_id == "2"
    story2_1 = epic2.stories[0]
    assert len(story2_1.acceptance_criteria) == 2