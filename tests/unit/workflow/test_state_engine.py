import os
import tempfile
import json
from workflow import state_engine

def test_save_and_load_execution_state(tmp_path):
    project_dir = tmp_path
    wf = {
        "workflow_id": "wf-test-123",
        "current_stage": "document_processing",
        "project_path": str(project_dir),
        "status": "running",
    }
    saved_path = state_engine.save_execution_state(str(project_dir), wf)
    assert os.path.exists(saved_path)
    loaded = state_engine.load_execution_state(str(project_dir), wf["workflow_id"])
    assert loaded["workflow_id"] == wf["workflow_id"]
    assert loaded["current_stage"] == wf["current_stage"]

def test_save_and_load_checkpoint(tmp_path):
    project_dir = tmp_path
    workflow_id = "wf-test-456"
    checkpoint_name = "after_shard"
    checkpoint_payload = {
        "workflow_state": {
            "workflow_id": workflow_id,
            "current_stage": "epic_creation",
        },
        "shard_result": {"success": True, "sharded_files": ["docs/prd/shard1.md"]},
    }
    cp_path = state_engine.save_checkpoint(str(project_dir), workflow_id, checkpoint_name, checkpoint_payload)
    assert os.path.exists(cp_path)
    loaded_cp = state_engine.load_checkpoint(str(project_dir), workflow_id, checkpoint_name)
    assert loaded_cp["workflow_state"]["workflow_id"] == workflow_id
    # listing checkpoints
    cps = state_engine.list_checkpoints(str(project_dir), workflow_id)
    assert any(checkpoint_name in name for name in cps)

def test_restore_from_checkpoint(tmp_path):
    project_dir = tmp_path
    workflow_id = "wf-test-789"
    checkpoint_name = "ck1"
    checkpoint_payload = {
        "workflow_state": {
            "workflow_id": workflow_id,
            "current_stage": "epic_creation",
            "status": "paused"
        }
    }
    state_engine.save_checkpoint(str(project_dir), workflow_id, checkpoint_name, checkpoint_payload)
    restored = state_engine.restore_from_checkpoint(str(project_dir), workflow_id, checkpoint_name)
    assert restored["workflow_id"] == workflow_id
    assert restored["current_stage"] == "epic_creation"