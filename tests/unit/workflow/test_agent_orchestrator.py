import asyncio
import pytest
import json

from workflow.agent_orchestrator import AgentOrchestrator
from workflow.models import TaskExecution


@pytest.mark.asyncio
async def test_activate_and_assign_task():
    orch = AgentOrchestrator()
    task = TaskExecution(task_id="t1", story_id="2.1", agent_id=None, task_type="code", status="pending")
    session = await orch.activate_agent("dev-agent", context={"k": "v"})
    assert session.agent_id == "dev-agent"
    assigned = await orch.assign_task("dev-agent", task)
    assert assigned.status == "running"
    assert assigned.agent_id == "dev-agent"
    # complete
    completed = await orch.record_task_complete(assigned, result={"ok": True})
    assert completed.status == "completed"
    assert completed.execution_time is not None


def test_transfer_context():
    orch = AgentOrchestrator()
    ok = asyncio.get_event_loop().run_until_complete(orch.transfer_context("a1", "a2", {"x": 1}))
    assert ok is True
    # verify json output
    task = TaskExecution(task_id="t2", story_id=None, agent_id=None, task_type="no-op", status="pending")
    task.session_id = "s1"
    task.transfer_chain = ["assigned_to:a2"]
    j = orch.task_transfer_chain_json(task)
    assert '"task_id": "t2"' in j


@pytest.mark.asyncio
async def test_transfer_context_idempotent_merge():
    orch = AgentOrchestrator()
    # activate target agent to create a session
    session = await orch.activate_agent("target-agent", context={"initial": True})
    # perform first transfer
    ctx = {"x": 1}
    ok1 = await orch.transfer_context("source-agent", "target-agent", ctx)
    assert ok1 is True
    # capture merged transfers
    merged = session.context.get("_merged_transfers", [])
    assert isinstance(merged, list)
    assert len(merged) == 1
    transfer_id = merged[0]
    # perform the same transfer again (should be idempotent)
    # include the transfer_id to simulate retry
    ctx_retry = {"x": 1, "_transfer_id": transfer_id}
    ok2 = await orch.transfer_context("source-agent", "target-agent", ctx_retry)
    assert ok2 is True
    # merged should not duplicate the transfer_id
    merged_after = session.context.get("_merged_transfers", [])
    assert merged_after.count(transfer_id) == 1


@pytest.mark.asyncio
async def test_transfer_context_sanitization_and_retry(monkeypatch):
    orch = AgentOrchestrator()
    # activate target agent
    session = await orch.activate_agent("target-agent-2", context={})
    # create a non-serializable object in context
    class Bad:
        def __str__(self):
            return "bad-object"
    bad_ctx = {"ok": 2, "bad": Bad()}
    # should succeed and convert non-serializable to str
    ok = await orch.transfer_context("src", "target-agent-2", bad_ctx)
    assert ok is True
    assert session.context.get("ok") == 2
    assert "bad" in session.context
    assert isinstance(session.context["bad"], str)


# New comprehensive tests for Story 2.1 functionality

@pytest.mark.asyncio
async def test_event_driven_communication():
    """Test the event-driven communication system."""
    orch = AgentOrchestrator()
    
    # Track events received
    events_received = []
    
    def test_handler(event_data):
        events_received.append(event_data)
    
    # Subscribe to events
    orch.subscribe_to_event("test_event", test_handler)
    
    # Emit an event
    await orch.emit_event("test_event", {"message": "test", "data": 123})
    
    # Verify event was received
    assert len(events_received) == 1
    assert events_received[0]["message"] == "test"
    assert events_received[0]["data"] == 123


@pytest.mark.asyncio
async def test_agent_status_monitoring():
    """Test agent status monitoring and health checks."""
    orch = AgentOrchestrator()
    
    # Initially inactive agent
    status = await orch.get_agent_status("test-agent")
    assert status["status"] == "inactive"
    
    # Activate agent
    session = await orch.activate_agent("test-agent", {"initial": True})
    
    # Check status after activation
    status = await orch.get_agent_status("test-agent")
    assert status["agent_id"] == "test-agent" 
    assert status["status"] == "active"
    assert status["sessions"] == 1
    assert status["active_sessions"] == 1
    
    # Health check
    health = await orch.health_check_agent("test-agent")
    assert health is True


@pytest.mark.asyncio
async def test_load_balancing():
    """Test load balancing functionality."""
    orch = AgentOrchestrator()
    
    # Create some agent sessions with different loads
    await orch.activate_agent("agent1", {})
    await orch.activate_agent("agent2", {})
    await orch.activate_agent("agent3", {})
    
    # Create tasks to simulate load
    task1 = TaskExecution(task_id="load1", agent_id=None, task_type="work", status="pending")
    task2 = TaskExecution(task_id="load2", agent_id=None, task_type="work", status="pending")
    
    await orch.assign_task("agent1", task1)  # agent1 has 1 active task
    await orch.assign_task("agent1", task2)  # agent1 has 2 active tasks
    
    # Get load metrics
    metrics = await orch.get_agent_load_metrics()
    assert "agent1" in metrics
    assert "agent2" in metrics
    assert metrics["agent1"]["active_sessions"] >= metrics["agent2"]["active_sessions"]
    
    # Test least loaded agent selection
    least_loaded = await orch.get_least_loaded_agent(["agent1", "agent2", "agent3"])
    assert least_loaded in ["agent2", "agent3"]  # Should be one of the less loaded agents


@pytest.mark.asyncio
async def test_workflow_automation():
    """Test workflow automation and state machine."""
    orch = AgentOrchestrator()
    
    # Track workflow events
    workflow_events = []
    
    def workflow_handler(event_data):
        workflow_events.append(event_data)
    
    # Setup workflow automation
    await orch.setup_workflow_automation()
    
    # Subscribe to workflow completion events
    orch.subscribe_to_event("workflow_completed", workflow_handler)
    
    # Trigger a workflow manually
    await orch.trigger_workflow("test-story-2.1", {"requirement": "implement agent transfer"})
    
    # Simulate development completion
    await orch.emit_event("development_completed", {
        "story_id": "test-story-2.1",
        "context": {"code_files": ["agent_orchestrator.py"]}
    })
    
    # Simulate QA completion with approval
    await orch.emit_event("qa_completed", {
        "story_id": "test-story-2.1", 
        "result": {"status": "approved"},
        "context": {"code_files": ["agent_orchestrator.py"]}
    })
    
    # Verify workflow completed event was emitted
    assert len(workflow_events) == 1
    assert workflow_events[0]["story_id"] == "test-story-2.1"
    assert workflow_events[0]["final_status"] == "completed"


@pytest.mark.asyncio
async def test_comprehensive_status_report():
    """Test comprehensive agent status reporting."""
    orch = AgentOrchestrator()
    
    # Create various agent sessions
    await orch.activate_agent("dev", {"task": "development"})
    await orch.activate_agent("qa", {"task": "testing"})
    await orch.activate_agent("sm", {"task": "management"})
    
    # Generate status report
    report = await orch.generate_agent_status_report()
    
    assert "timestamp" in report
    assert "total_sessions" in report
    assert "agents" in report
    assert "system_health" in report
    assert report["total_sessions"] == 3
    assert len(report["agents"]) == 3
    assert "dev" in report["agents"]
    assert "qa" in report["agents"] 
    assert "sm" in report["agents"]


@pytest.mark.asyncio
async def test_task_execution_tracking():
    """Test comprehensive task execution tracking."""
    orch = AgentOrchestrator()
    
    # Create and track a complete task lifecycle
    task = TaskExecution(
        task_id="story-2.1-task",
        story_id="2.1",
        agent_id=None,
        task_type="development",
        status="pending",
        input_context={"requirements": "implement agent transfer"}
    )
    
    # Assign task
    assigned_task = await orch.assign_task("dev", task)
    assert assigned_task.status == "running"
    assert assigned_task.agent_id == "dev"
    assert "assigned_to:dev" in assigned_task.transfer_chain
    
    # Complete task
    result = {"files_created": ["agent_orchestrator.py"], "tests_passed": True}
    completed_task = await orch.record_task_complete(assigned_task, result)
    
    assert completed_task.status == "completed"
    assert completed_task.execution_time is not None
    assert completed_task.notification_sent is True
    assert "completed" in completed_task.transfer_chain
    
    # Verify task transfer chain JSON
    chain_json = orch.task_transfer_chain_json(completed_task)
    chain_data = json.loads(chain_json)
    assert chain_data["task_id"] == "story-2.1-task"
    assert chain_data["agent_id"] == "dev"
    assert "assigned_to:dev" in chain_data["transfer_chain"]
    assert "completed" in chain_data["transfer_chain"]


@pytest.mark.asyncio  
async def test_session_watchdog_cleanup():
    """Test session timeout and cleanup mechanism."""
    # Create orchestrator with very short timeout for testing
    orch = AgentOrchestrator()
    orch._session_timeout = 0.1  # 100ms timeout
    
    # Activate agent
    session = await orch.activate_agent("temp-agent", {})
    session_id = session.session_id
    
    # Verify session exists
    assert session_id in orch._sessions
    
    # Wait for timeout and cleanup
    await asyncio.sleep(0.15)  # Wait longer than timeout
    
    # Session should be cleaned up (this is probabilistic as watchdog runs in background)
    # We check if the session is marked as completed or removed
    remaining_session = orch._sessions.get(session_id)
    if remaining_session:
        # If still exists, it should be marked as completed
        assert remaining_session.status.value == "completed"


@pytest.mark.asyncio
async def test_error_handling_and_recovery():
    """Test error handling in various scenarios."""
    orch = AgentOrchestrator()
    
    # Test invalid context transfer
    result = await orch.transfer_context("agent1", "agent2", "invalid_context")
    assert result is False
    
    # Test health check for non-existent agent
    health = await orch.health_check_agent("non-existent")
    assert health is False
    
    # Test status for non-existent agent
    status = await orch.get_agent_status("non-existent")
    assert status["status"] == "inactive"
    assert status["sessions"] == 0

