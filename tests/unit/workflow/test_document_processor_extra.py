import sys
from pathlib import Path
import pytest
import os
import tempfile

# Ensure repo root is importable
ROOT = Path(__file__).resolve().parents[4]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from workflow.document_processor import _kebab_case, validate_document_structure, shard_documents, ShardingResult

def test_kebab_case_edge_cases():
    assert _kebab_case("  Hello World!  ") == "hello-world"
    assert _kebab_case("$$$") == "untitled"
    assert _kebab_case("标题 包含 中文") == "标题-包含-中文"
    assert _kebab_case("") == "untitled"

def test_validate_unclosed_fence(tmp_path):
    p = tmp_path / "unclosed.md"
    # single opening fence without a closing fence
    p.write_text("## Section\n\n```\ncode block without end\n", encoding="utf-8")
    res = validate_document_structure(str(p))
    assert res.valid is True or res.valid is False  # valid may be false depending on implementation
    # But must contain a warning about possible unclosed fenced code block
    assert any("unclosed" in w.lower() for w in res.warnings)

def test_shard_documents_write_permission_error(tmp_path, monkeypatch):
    docs = tmp_path / "docs"
    docs.mkdir()
    prd = docs / "prd.md"
    prd.write_text("# Title\n\n## Sec\nContent\n", encoding="utf-8")

    # Monkeypatch Path.write_text to raise on shard target files to simulate permission error
    original_write_text = Path.write_text

    def fake_write_text(self, data, encoding=None):
        # allow reading the source file but fail when writing to target shard files
        if str(self).endswith(".md") and self.parent != prd.parent:
            raise PermissionError("Simulated permission error")
        return original_write_text(self, data, encoding=encoding)

    monkeypatch.setattr(Path, "write_text", fake_write_text)

    res: ShardingResult = shard_documents(str(prd))
    # Since we simulated a write failure, the result should indicate failure and include the error message
    assert res.success is False
    assert any("permission" in e.lower() or "simulated" in e.lower() for e in res.errors)

def test_md_tree_partial_failure_returns_error(tmp_path, monkeypatch):
    # Simulate a project with .bmad-core enabling markdownExploder and a fake md-tree that exits non-zero
    prj = tmp_path
    docs = prj / "docs"
    docs.mkdir()
    prd = docs / "prd.md"
    prd.write_text("# Title\n\n## A\nx\n", encoding="utf-8")

    core = prj / ".bmad-core"
    core.mkdir()
    cfg = core / "core-config.yaml"
    cfg.write_text("markdownExploder: true\n", encoding="utf-8")

    fake_bin = prj / "bin"
    fake_bin.mkdir()
    fake_md = fake_bin / "md-tree"
    fake_md.write_text("#!/usr/bin/env python3\nimport sys\nprint('error', file=sys.stderr)\nraise SystemExit(2)\n", encoding="utf-8")
    fake_md.chmod(0o755)

    # Ensure shutil.which finds our fake md-tree
    import shutil
    monkeypatch.setattr(shutil, "which", lambda name: str(fake_md) if name == "md-tree" else None)

    res: ShardingResult = shard_documents(str(prd))
    # On md-tree failure we expect a ShardingResult with success False and an md-tree error included
    assert res.success is False
    assert any("md-tree" in e.lower() or "error" in e.lower() for e in res.errors)