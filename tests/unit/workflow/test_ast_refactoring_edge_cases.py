"""
测试 AST 重构的边界情况，包括注释与格式保持、复杂导入等
"""
import ast
import pytest
import tempfile
from pathlib import Path

from workflow.code_generator import CodeGenerator
from workflow.models import FileSpec


class TestASTRefactoringEdgeCases:
    """测试 AST 重构的边界情况"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = Path(self.temp_dir)
        self.generator = CodeGenerator(str(self.project_root))
    
    def teardown_method(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_preserve_comments_in_class(self):
        """测试在类重构时保持注释"""
        original_code = '''
class TestClass:
    """这是一个测试类的文档字符串"""
    
    # 这是一个重要的注释
    def __init__(self):
        # 初始化注释
        self.value = 42  # 行尾注释
    
    def method1(self):
        """方法1的文档字符串"""
        # 方法内注释
        return self.value
        
    # 类级别注释
    def method2(self):
        pass
'''
        
        # 创建测试文件
        test_file = self.project_root / "test_class.py"
        test_file.write_text(original_code)
        
        # 解析 AST
        tree = ast.parse(original_code)
        
        # 验证 AST 解析成功
        assert isinstance(tree, ast.Module)
        assert len(tree.body) == 1
        assert isinstance(tree.body[0], ast.ClassDef)
        
        # 验证类结构
        class_node = tree.body[0]
        assert class_node.name == "TestClass"
        # 类体包含：文档字符串(Expr), __init__, method1, method2
        assert len(class_node.body) == 4

        # 验证方法数量（排除文档字符串）
        methods = [node for node in class_node.body if isinstance(node, ast.FunctionDef)]
        assert len(methods) == 3  # __init__, method1, method2
    
    def test_complex_imports_preservation(self):
        """测试复杂导入语句的保持"""
        complex_imports = '''
# 标准库导入
import os
import sys
from pathlib import Path

# 第三方库导入
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from typing import Dict, List, Optional, Union, Tuple

# 相对导入
from .models import BaseModel
from ..utils import helper_function
from ...config import settings

# 条件导入
try:
    import ujson as json
except ImportError:
    import json

# 别名导入
from very_long_module_name import (
    VeryLongClassName as VLC,
    another_long_function_name as alf,
    CONSTANT_VALUE
)

class TestClass:
    pass
'''
        
        # 解析 AST
        tree = ast.parse(complex_imports)
        
        # 验证导入语句数量和类型
        import_nodes = [node for node in tree.body if isinstance(node, (ast.Import, ast.ImportFrom))]
        assert len(import_nodes) >= 8  # 至少有8个导入语句
        
        # 验证特定导入类型
        has_regular_import = any(isinstance(node, ast.Import) for node in import_nodes)
        has_from_import = any(isinstance(node, ast.ImportFrom) for node in import_nodes)
        has_relative_import = any(node.level > 0 for node in import_nodes if isinstance(node, ast.ImportFrom))
        
        assert has_regular_import
        assert has_from_import
        assert has_relative_import
    
    def test_nested_class_and_function_structure(self):
        """测试嵌套类和函数结构的处理"""
        nested_code = '''
class OuterClass:
    """外部类"""
    
    class InnerClass:
        """内部类"""
        
        def inner_method(self):
            """内部方法"""
            
            def nested_function():
                """嵌套函数"""
                return "nested"
            
            return nested_function()
    
    def outer_method(self):
        """外部方法"""
        
        class LocalClass:
            """局部类"""
            pass
        
        def local_function():
            """局部函数"""
            
            def deeply_nested():
                """深度嵌套函数"""
                return LocalClass()
            
            return deeply_nested()
        
        return local_function()
'''
        
        # 解析 AST
        tree = ast.parse(nested_code)
        
        # 验证嵌套结构
        outer_class = tree.body[0]
        assert isinstance(outer_class, ast.ClassDef)
        assert outer_class.name == "OuterClass"
        
        # 查找内部类
        inner_class = None
        outer_method = None
        
        for node in outer_class.body:
            if isinstance(node, ast.ClassDef) and node.name == "InnerClass":
                inner_class = node
            elif isinstance(node, ast.FunctionDef) and node.name == "outer_method":
                outer_method = node
        
        assert inner_class is not None
        assert outer_method is not None
        
        # 验证内部类有方法
        assert len(inner_class.body) > 0
        assert any(isinstance(node, ast.FunctionDef) for node in inner_class.body)
    
    def test_decorator_preservation(self):
        """测试装饰器的保持"""
        decorated_code = '''
from functools import wraps
from typing import Callable

def my_decorator(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)
    return wrapper

@my_decorator
@property
class DecoratedClass:
    """带装饰器的类"""
    
    @staticmethod
    def static_method():
        """静态方法"""
        pass
    
    @classmethod
    def class_method(cls):
        """类方法"""
        pass
    
    @property
    def prop(self):
        """属性"""
        return self._value
    
    @prop.setter
    def prop(self, value):
        """属性设置器"""
        self._value = value
    
    @my_decorator
    def decorated_method(self):
        """装饰的方法"""
        pass
'''
        
        # 解析 AST
        tree = ast.parse(decorated_code)
        
        # 查找装饰的类
        decorated_class = None
        for node in tree.body:
            if isinstance(node, ast.ClassDef) and node.name == "DecoratedClass":
                decorated_class = node
                break
        
        assert decorated_class is not None
        assert len(decorated_class.decorator_list) == 2  # @my_decorator 和 @property
        
        # 验证方法装饰器
        decorated_methods = []
        for node in decorated_class.body:
            if isinstance(node, ast.FunctionDef) and node.decorator_list:
                decorated_methods.append((node.name, len(node.decorator_list)))
        
        assert len(decorated_methods) >= 4  # 至少4个装饰的方法
    
    def test_complex_expressions_and_comprehensions(self):
        """测试复杂表达式和推导式的处理"""
        complex_code = '''
class DataProcessor:
    def process_data(self, data):
        # 列表推导式
        filtered = [x for x in data if x > 0]
        
        # 字典推导式
        mapped = {k: v**2 for k, v in data.items() if isinstance(v, (int, float))}
        
        # 集合推导式
        unique = {x.lower() for x in data if isinstance(x, str)}
        
        # 生成器表达式
        generator = (x * 2 for x in filtered)
        
        # 复杂的嵌套表达式
        result = [
            {
                'original': item,
                'processed': item ** 2 if isinstance(item, (int, float)) else str(item).upper(),
                'metadata': {
                    'type': type(item).__name__,
                    'length': len(str(item))
                }
            }
            for item in data
            if item is not None
        ]
        
        # Lambda 表达式
        transform = lambda x: x * 2 if x > 0 else -x
        
        return result, transform
'''
        
        # 解析 AST
        tree = ast.parse(complex_code)
        
        # 验证类结构
        data_processor = tree.body[0]
        assert isinstance(data_processor, ast.ClassDef)
        assert data_processor.name == "DataProcessor"
        
        # 验证方法存在
        process_method = data_processor.body[0]
        assert isinstance(process_method, ast.FunctionDef)
        assert process_method.name == "process_data"
        
        # 验证方法体包含复杂表达式
        assert len(process_method.body) > 5  # 至少有多个语句
    
    def test_exception_handling_structures(self):
        """测试异常处理结构的保持"""
        exception_code = '''
class ErrorHandler:
    def handle_errors(self, operation):
        try:
            # 尝试执行操作
            result = operation()
            
            try:
                # 嵌套的 try-except
                validated = self.validate(result)
                return validated
            except ValidationError as ve:
                # 特定异常处理
                self.log_validation_error(ve)
                raise
            except Exception as e:
                # 通用异常处理
                self.log_error(f"Validation failed: {e}")
                return None
                
        except (TypeError, ValueError) as e:
            # 多个异常类型
            self.log_error(f"Type or value error: {e}")
            return None
        except Exception as e:
            # 通用异常
            self.log_error(f"Unexpected error: {e}")
            raise
        else:
            # 无异常时执行
            self.log_success("Operation completed successfully")
        finally:
            # 总是执行
            self.cleanup()
'''
        
        # 解析 AST
        tree = ast.parse(exception_code)
        
        # 验证异常处理结构
        error_handler = tree.body[0]
        assert isinstance(error_handler, ast.ClassDef)
        
        handle_method = error_handler.body[0]
        assert isinstance(handle_method, ast.FunctionDef)
        
        # 查找 try-except 语句
        try_statements = []
        for node in ast.walk(handle_method):
            if isinstance(node, ast.Try):
                try_statements.append(node)
        
        assert len(try_statements) >= 2  # 至少有2个 try 语句（包括嵌套的）
        
        # 验证异常处理器
        main_try = try_statements[0]  # 主要的 try 语句
        assert len(main_try.handlers) >= 2  # 至少有2个异常处理器
        assert main_try.orelse is not None  # 有 else 子句
        assert main_try.finalbody is not None  # 有 finally 子句
