"""
测试文件操作的失败场景，包括权限、磁盘、并发冲突等
"""
import os
import pytest
import tempfile
import threading
import time
from pathlib import Path
from unittest.mock import patch, Mock, MagicMock

from workflow.code_generator import CodeGenerator
from workflow.document_processor import shard_documents
from workflow.models import FileSpec


class TestFileOperationFailures:
    """测试文件操作的各种失败场景"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = Path(self.temp_dir)
        self.generator = CodeGenerator(str(self.project_root))
    
    def teardown_method(self):
        """清理测试环境"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_create_file_permission_denied(self):
        """测试创建文件时权限被拒绝"""
        # 创建一个只读目录
        readonly_dir = self.project_root / "readonly"
        readonly_dir.mkdir()
        readonly_dir.chmod(0o444)  # 只读权限
        
        file_specs = [
            FileSpec(
                path="readonly/test.py",
                content="print('test')",
                language="python",
                template=None,
                parameters={},
                operation="create"
            )
        ]
        
        # 应该抛出权限异常
        with pytest.raises(PermissionError):
            self.generator.create_files(file_specs)
    
    def test_modify_file_permission_denied(self):
        """测试修改文件时权限被拒绝"""
        # 创建一个只读文件
        readonly_file = self.project_root / "readonly.py"
        readonly_file.write_text("original content")
        readonly_file.chmod(0o444)  # 只读权限
        
        file_specs = [
            FileSpec(
                path="readonly.py",
                content="modified content",
                language="python",
                template=None,
                parameters={},
                operation="modify"
            )
        ]
        
        # 应该抛出权限异常
        with pytest.raises(PermissionError):
            self.generator.modify_files(file_specs)
    
    def test_backup_file_disk_full(self):
        """测试备份文件时磁盘空间不足"""
        # 创建测试文件
        test_file = self.project_root / "test.py"
        test_file.write_text("test content")
        
        # 模拟磁盘空间不足
        with patch('shutil.copy2', side_effect=OSError("No space left on device")):
            with pytest.raises(OSError, match="No space left on device"):
                self.generator.backup_files(["test.py"])
    
    def test_rollback_file_backup_missing(self):
        """测试回滚时备份文件丢失"""
        backup_mapping = {
            "test.py": "backups/test_backup.py"
        }
        
        # 备份文件不存在，应该记录错误但不抛出异常
        rolled_back = self.generator.rollback_files(backup_mapping)
        assert len(rolled_back) == 0
    
    def test_concurrent_file_modification(self):
        """测试并发文件修改冲突"""
        test_file = self.project_root / "concurrent.py"
        test_file.write_text("original content")
        
        results = []
        errors = []
        
        def modify_file(content_suffix):
            try:
                file_specs = [
                    FileSpec(
                        path="concurrent.py",
                        content=f"modified content {content_suffix}",
                        language="python",
                        template=None,
                        parameters={},
                        operation="modify"
                    )
                ]
                result = self.generator.modify_files(file_specs)
                results.append(result)
            except Exception as e:
                errors.append(str(e))
        
        # 启动多个线程同时修改文件
        threads = []
        for i in range(5):
            thread = threading.Thread(target=modify_file, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证至少有一些操作成功
        assert len(results) > 0 or len(errors) > 0
    
    def test_shard_documents_invalid_path(self):
        """测试文档分片时路径无效"""
        invalid_path = "/nonexistent/path/document.md"

        result = shard_documents(invalid_path)
        assert result.success is False
        assert len(result.errors) > 0
        assert any("does not exist" in error.lower() or "not found" in error.lower() or "no such file" in error.lower()
                  for error in result.errors)
    
    def test_file_operation_interrupted(self):
        """测试文件操作被中断"""
        test_file = self.project_root / "interrupt_test.py"
        
        # 模拟操作被中断
        with patch('builtins.open', side_effect=KeyboardInterrupt("Operation interrupted")):
            file_specs = [
                FileSpec(
                    path="interrupt_test.py",
                    content="test content",
                    language="python",
                    template=None,
                    parameters={},
                    operation="create"
                )
            ]
            
            with pytest.raises(KeyboardInterrupt):
                self.generator.create_files(file_specs)
    
    def test_file_system_readonly(self):
        """测试文件系统只读"""
        # 模拟只读文件系统 - 需要patch正确的方法
        with patch('builtins.open', side_effect=OSError("Read-only file system")):
            file_specs = [
                FileSpec(
                    path="readonly_fs_test.py",
                    content="test content",
                    language="python",
                    template=None,
                    parameters={},
                    operation="create"
                )
            ]

            with pytest.raises(OSError, match="Read-only file system"):
                self.generator.create_files(file_specs)
    
    def test_network_drive_disconnected(self):
        """测试网络驱动器断开连接"""
        # 模拟网络驱动器断开
        with patch('pathlib.Path.exists', side_effect=OSError("Network is unreachable")):
            with pytest.raises(OSError, match="Network is unreachable"):
                self.generator.backup_files(["test.py"])
    
    def test_file_locked_by_another_process(self):
        """测试文件被其他进程锁定"""
        test_file = self.project_root / "locked.py"
        test_file.write_text("original content")
        
        # 模拟文件被锁定
        with patch('builtins.open', side_effect=PermissionError("The process cannot access the file because it is being used by another process")):
            file_specs = [
                FileSpec(
                    path="locked.py",
                    content="modified content",
                    language="python",
                    template=None,
                    parameters={},
                    operation="modify"
                )
            ]
            
            with pytest.raises(PermissionError, match="being used by another process"):
                self.generator.modify_files(file_specs)
