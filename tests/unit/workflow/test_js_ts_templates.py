import os
from pathlib import Path

from workflow.template_manager import Template<PERSON><PERSON><PERSON>


def test_discover_and_render_js_templates(tmp_path: Path):
    # Use real repo templates
    template_root = Path(__file__).resolve().parents[3] / "workflow" / "templates"
    manager = TemplateManager(str(template_root))

    # Discover JS templates
    js_templates = manager.get_available_templates(language="javascript")
    names = {t["name"] for t in js_templates}
    assert "js_class.js" in names
    assert "js_function.js" in names

    # Render js_class
    content = manager.render_template(
        "js_class.js",
        "javascript",
        {
            "class_name": "ApiClient",
            "methods": [
                {"name": "get", "params": ["url"], "body": "return fetch(url);"},
            ],
        },
    )
    assert "export class ApiClient" in content
    assert "get(url)" in content

    # Render js_function
    content = manager.render_template(
        "js_function.js",
        "javascript",
        {"function_name": "sum", "params": ["a", "b"], "body": "return a + b;"},
    )
    assert "export function sum" in content
    assert "return a + b;" in content


def test_discover_and_render_ts_templates(tmp_path: Path):
    template_root = Path(__file__).resolve().parents[3] / "workflow" / "templates"
    manager = TemplateManager(str(template_root))

    # Discover TS templates
    ts_templates = manager.get_available_templates(language="typescript")
    names = {t["name"] for t in ts_templates}
    assert "ts_class.ts" in names
    assert "ts_interface.ts" in names

    # Render ts_class
    content = manager.render_template(
        "ts_class.ts",
        "typescript",
        {
            "class_name": "Store",
            "constructor_params": ["initial"],
            "constructor_body": "this.state = initial;",
            "methods": [
                {
                    "name": "getState",
                    "params": [],
                    "return_type": "any",
                    "body": "return this.state;",
                }
            ],
        },
    )
    assert "export class Store" in content
    assert "constructor(initial)" in content
    assert "getState()" in content

    # Render ts_interface
    content = manager.render_template(
        "ts_interface.ts",
        "typescript",
        {
            "interface_name": "User",
            "properties": [
                {"name": "id", "type": "string"},
                {"name": "age", "type": "number"},
            ],
        },
    )
    assert "export interface User" in content
    assert "id: string;" in content
    assert "age: number;" in content


