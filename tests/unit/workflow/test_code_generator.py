"""
测试代码生成引擎
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

from workflow.code_generator import CodeGenerator
from workflow.models import (
    CodeGenerationResult, FileSpec, AgentSession, TaskExecution,
    SharedContext, ContextCache, AgentStatus, WorkflowExecution, UserInteraction
)
from datetime import datetime


class TestCodeGenerator:
    """测试 CodeGenerator 类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.project_root = Path(self.temp_dir)
        self.template_dir = self.project_root / "workflow" / "templates"
        
        # 创建模板目录
        self.template_dir.mkdir(parents=True, exist_ok=True)
        (self.template_dir / "python").mkdir(exist_ok=True)
        
        # 创建简单的测试模板
        test_template = """class {{ class_name }}:
    def __init__(self):
        pass"""
        
        with open(self.template_dir / "python" / "class.py.j2", 'w') as f:
            f.write(test_template)
        
        self.generator = CodeGenerator(str(self.project_root))
    
    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        assert self.generator.project_root == self.project_root
        assert self.generator.template_dir == self.template_dir
        assert 'python' in self.generator.supported_languages
        assert 'javascript' in self.generator.supported_languages
        assert 'typescript' in self.generator.supported_languages
    
    def test_create_files(self):
        """测试创建文件"""
        file_specs = [
            FileSpec(
                path="test_file.py",
                content="# Test file\nprint('Hello, World!')",
                language="python",
                template=None,
                parameters={},
                operation="create"
            )
        ]
        
        result = self.generator.create_files(file_specs)
        
        assert len(result) == 1
        assert result[0] == "test_file.py"
        
        # 验证文件是否创建
        test_file = self.project_root / "test_file.py"
        assert test_file.exists()
        
        with open(test_file, 'r') as f:
            content = f.read()
        assert "# Test file" in content
        assert "print('Hello, World!')" in content
    
    def test_create_files_with_template(self):
        """测试使用模板创建文件"""
        file_specs = [
            FileSpec(
                path="generated_class.py",
                content="",
                language="python",
                template="class.py.j2",
                parameters={"class_name": "TestClass"},
                operation="create"
            )
        ]
        
        result = self.generator.create_files(file_specs)
        
        assert len(result) == 1
        assert result[0] == "generated_class.py"
        
        # 验证文件是否创建
        test_file = self.project_root / "generated_class.py"
        assert test_file.exists()
        
        with open(test_file, 'r') as f:
            content = f.read()
        assert "class TestClass:" in content
    
    def test_modify_files_new_file(self):
        """测试修改不存在的文件（应该创建新文件）"""
        file_specs = [
            FileSpec(
                path="new_file.py",
                content="# New file content",
                language="python",
                template=None,
                parameters={},
                operation="modify"
            )
        ]
        
        result = self.generator.modify_files(file_specs)
        
        assert len(result) == 1
        assert result[0] == "new_file.py"
        
        # 验证文件是否创建
        test_file = self.project_root / "new_file.py"
        assert test_file.exists()
    
    def test_modify_files_existing_file(self):
        """测试修改现有文件"""
        # 先创建一个文件
        test_file = self.project_root / "existing_file.py"
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("# Original content")
        
        file_specs = [
            FileSpec(
                path="existing_file.py",
                content="# Modified content",
                language="python",
                template=None,
                parameters={},
                operation="modify"
            )
        ]
        
        result = self.generator.modify_files(file_specs)
        
        assert len(result) == 1
        assert result[0] == "existing_file.py"
        
        # 验证文件内容是否修改
        with open(test_file, 'r') as f:
            content = f.read()
        assert "# Modified content" in content
    
    def test_generate_code_success(self):
        """测试成功的代码生成"""
        story = {
            "id": "test_story",
            "title": "Test Story"
        }
        
        task = {
            "name": "Create CodeGenerator class",
            "description": "Create CodeGenerator class in workflow/code_generator.py",
            "operation_type": "create"
        }
        
        with patch.object(self.generator, '_parse_task_requirements') as mock_parse:
            mock_parse.return_value = [
                FileSpec(
                    path="test_generated.py",
                    content="# Generated file",
                    language="python",
                    template=None,
                    parameters={},
                    operation="create"
                )
            ]
            
            result = self.generator.generate_code(story, task)
            
            assert isinstance(result, CodeGenerationResult)
            assert result.success is True
            assert len(result.generated_files) == 1
            assert result.generated_files[0] == "test_generated.py"
            assert len(result.errors) == 0
    
    def test_generate_code_with_errors(self):
        """测试有错误的代码生成"""
        story = {"id": "test_story"}
        task = {"name": "Test task"}
        
        with patch.object(self.generator, '_parse_task_requirements') as mock_parse:
            # 模拟解析错误
            mock_parse.side_effect = Exception("Parse error")
            
            result = self.generator.generate_code(story, task)
            
            assert isinstance(result, CodeGenerationResult)
            assert result.success is False
            assert len(result.errors) == 1
            assert "Code generation failed" in result.errors[0]
    
    def test_analyze_code_quality(self):
        """测试代码质量分析"""
        # 创建测试 Python 文件
        test_file = self.project_root / "quality_test.py"
        test_file.parent.mkdir(parents=True, exist_ok=True)
        
        test_content = """
def test_function():
    if True:
        for i in range(10):
            while i > 0:
                try:
                    pass
                except:
                    pass
"""
        
        with open(test_file, 'w') as f:
            f.write(test_content)
        
        metrics = self.generator._analyze_code_quality(["quality_test.py"])
        
        assert metrics['total_files'] == 1
        assert metrics['python_files'] == 1
        assert metrics['total_lines'] > 0
        assert metrics['complexity_score'] > 0
    
    def test_validate_code_syntax_error(self):
        """测试代码验证 - 语法错误"""
        # 创建有语法错误的 Python 文件
        test_file = self.project_root / "syntax_error.py"
        test_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(test_file, 'w') as f:
            f.write("def invalid_syntax(\n")  # 语法错误
        
        results = self.generator._validate_code(["syntax_error.py"])
        
        assert results['syntax_valid'] is False
        assert len(results['errors']) > 0
        assert "Syntax error" in results['errors'][0]
    
    def test_validate_code_valid_syntax(self):
        """测试代码验证 - 有效语法"""
        # 创建有效的 Python 文件
        test_file = self.project_root / "valid_syntax.py"
        test_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(test_file, 'w') as f:
            f.write("def valid_function():\n    pass\n")
        
        results = self.generator._validate_code(["valid_syntax.py"])
        
        assert results['syntax_valid'] is True
    
    def test_render_template(self):
        """测试模板渲染"""
        parameters = {"class_name": "RenderedClass"}
        
        result = self.generator._render_template("class.py.j2", parameters, "python")
        
        assert "class RenderedClass:" in result
    
    def test_calculate_complexity(self):
        """测试复杂度计算"""
        code = """
def complex_function():
    if True:
        for i in range(10):
            while i > 0:
                try:
                    pass
                except:
                    pass
"""
        tree = compile(code, '<string>', 'exec', flags=1024)  # AST mode
        import ast
        tree = ast.parse(code)
        
        complexity = self.generator._calculate_complexity(tree)
        assert complexity > 0
    
    @patch('subprocess.run')
    def test_validate_code_format_check(self, mock_run):
        """测试格式检查"""
        # 模拟 black 检查成功
        mock_run.return_value = Mock(returncode=0, stdout="")
        
        test_file = self.project_root / "format_test.py"
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test(): pass")
        
        results = self.generator._validate_code(["format_test.py"])
        
        assert results['format_valid'] is True
    
    @patch('subprocess.run')
    def test_validate_code_format_fail(self, mock_run):
        """测试格式检查失败"""
        # 模拟 black 检查失败
        mock_run.return_value = Mock(returncode=1, stdout="Format issues")
        
        test_file = self.project_root / "format_test.py"
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test(): pass")
        
        results = self.generator._validate_code(["format_test.py"])

        assert results['format_valid'] is False
        # 在新的实现中，格式化错误被添加到 errors 而不是 warnings
        assert len(results['errors']) > 0 or len(results['warnings']) > 0

    def test_delete_files(self):
        """测试删除多个文件"""
        # 创建测试文件
        test_files = ["delete_test1.py", "delete_test2.py"]
        for file_path in test_files:
            full_path = self.project_root / file_path
            with open(full_path, 'w') as f:
                f.write("# Test file")

        # 删除文件
        deleted_files = self.generator.delete_files(test_files)

        assert len(deleted_files) == 2
        assert "delete_test1.py" in deleted_files
        assert "delete_test2.py" in deleted_files

        # 验证文件已删除
        for file_path in test_files:
            full_path = self.project_root / file_path
            assert not full_path.exists()

    def test_rename_file(self):
        """测试重命名文件"""
        # 创建测试文件
        old_path = "rename_test_old.py"
        new_path = "rename_test_new.py"

        old_full_path = self.project_root / old_path
        new_full_path = self.project_root / new_path

        with open(old_full_path, 'w') as f:
            f.write("# Test file for rename")

        # 重命名文件
        result = self.generator.rename_file(old_path, new_path)

        assert result is True
        assert not old_full_path.exists()
        assert new_full_path.exists()

        # 验证内容
        with open(new_full_path, 'r') as f:
            content = f.read()
        assert "# Test file for rename" in content

    def test_rename_file_nonexistent(self):
        """测试重命名不存在的文件"""
        result = self.generator.rename_file("nonexistent.py", "new_name.py")
        assert result is False

    def test_backup_files(self):
        """测试备份文件"""
        # 创建测试文件
        test_files = ["backup_test1.py", "backup_test2.py"]
        for file_path in test_files:
            full_path = self.project_root / file_path
            with open(full_path, 'w') as f:
                f.write(f"# Content of {file_path}")

        # 备份文件
        backup_mapping = self.generator.backup_files(test_files)

        assert len(backup_mapping) == 2
        assert "backup_test1.py" in backup_mapping
        assert "backup_test2.py" in backup_mapping

        # 验证备份文件存在
        for original_path, backup_path in backup_mapping.items():
            backup_full_path = self.project_root / backup_path
            assert backup_full_path.exists()

            # 验证备份内容
            with open(backup_full_path, 'r') as f:
                content = f.read()
            assert f"# Content of {original_path}" in content

    def test_rollback_files(self):
        """测试回滚文件"""
        # 创建测试文件
        test_file = "rollback_test.py"
        original_content = "# Original content"
        modified_content = "# Modified content"

        full_path = self.project_root / test_file
        with open(full_path, 'w') as f:
            f.write(original_content)

        # 备份文件
        backup_mapping = self.generator.backup_files([test_file])

        # 修改原文件
        with open(full_path, 'w') as f:
            f.write(modified_content)

        # 验证文件已修改
        with open(full_path, 'r') as f:
            content = f.read()
        assert modified_content in content

        # 回滚文件
        rolled_back_files = self.generator.rollback_files(backup_mapping)

        assert len(rolled_back_files) == 1
        assert test_file in rolled_back_files

        # 验证文件已回滚
        with open(full_path, 'r') as f:
            content = f.read()
        assert original_content in content

    @patch('subprocess.run')
    def test_apply_code_formatting(self, mock_run):
        """测试代码格式化"""
        # 模拟 black 格式化成功
        mock_run.return_value = Mock(returncode=0, stdout="", stderr="")

        test_files = ["format_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test():pass")

        results = self.generator.apply_code_formatting(test_files)

        assert results['success'] is True
        assert test_files[0] in results['formatted_files']
        assert len(results['errors']) == 0

    @patch('subprocess.run')
    def test_run_code_linting(self, mock_run):
        """测试代码 lint 检查"""
        # 模拟 flake8 检查通过
        mock_run.return_value = Mock(returncode=0, stdout="", stderr="")

        test_files = ["lint_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test():\n    pass\n")

        results = self.generator.run_code_linting(test_files)

        assert results['success'] is True
        assert len(results['issues']) == 0

    @patch('subprocess.run')
    def test_run_code_linting_with_issues(self, mock_run):
        """测试代码 lint 检查发现问题"""
        # 模拟 flake8 发现问题
        mock_run.return_value = Mock(returncode=1, stdout="lint_test.py:1:1: E302 expected 2 blank lines", stderr="")

        test_files = ["lint_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test(): pass")

        results = self.generator.run_code_linting(test_files)

        assert results['success'] is False
        assert len(results['issues']) > 0
        assert "E302" in results['issues'][0]

    @patch('subprocess.run')
    def test_run_type_checking(self, mock_run):
        """测试类型检查"""
        # 模拟 mypy 检查通过
        mock_run.return_value = Mock(returncode=0, stdout="", stderr="")

        test_files = ["type_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test() -> None:\n    pass\n")

        results = self.generator.run_type_checking(test_files)

        assert results['success'] is True
        assert len(results['issues']) == 0

    def test_validate_custom_rules_print_violation(self):
        """测试自定义规则验证 - print 语句违规"""
        test_files = ["custom_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test():\n    print('Hello, World!')\n")

        results = self.generator.validate_custom_rules(test_files)

        assert results['success'] is False
        assert len(results['violations']) > 0
        assert "print()" in results['violations'][0]

    def test_validate_custom_rules_missing_type_hints(self):
        """测试自定义规则验证 - 缺少类型提示"""
        test_files = ["type_hint_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("def test_function(param):\n    return param\n")

        results = self.generator.validate_custom_rules(test_files)

        # 应该有警告但不是错误
        assert len(results['warnings']) > 0
        assert any("missing type hint" in warning for warning in results['warnings'])

    def test_validate_custom_rules_valid_code(self):
        """测试自定义规则验证 - 有效代码"""
        test_files = ["valid_test.py"]
        test_file = self.project_root / test_files[0]
        test_file.parent.mkdir(parents=True, exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("import logging\n\ndef test_function(param: str) -> str:\n    logging.info('Test')\n    return param\n")

        results = self.generator.validate_custom_rules(test_files)

        assert results['success'] is True
        assert len(results['violations']) == 0

    def test_analyze_code_structure(self):
        """测试代码结构分析"""
        test_file = "structure_test.py"
        test_content = '''"""Module docstring"""
import os
from typing import Dict, List

class TestClass:
    """Test class docstring"""

    def __init__(self):
        pass

    def method_with_complexity(self, param: str) -> str:
        """Method with some complexity"""
        if param:
            for i in range(10):
                if i > 5:
                    try:
                        return str(i)
                    except:
                        pass
        return "default"

def standalone_function(x: int) -> int:
    """Standalone function"""
    return x * 2
'''

        full_path = self.project_root / test_file
        with open(full_path, 'w') as f:
            f.write(test_content)

        analysis = self.generator.analyze_code_structure(test_file)

        assert 'classes' in analysis
        assert 'functions' in analysis
        assert 'imports' in analysis
        assert 'complexity' in analysis
        assert 'docstring_coverage' in analysis

        # 验证类分析
        assert len(analysis['classes']) == 1
        test_class = analysis['classes'][0]
        assert test_class['name'] == 'TestClass'
        assert test_class['docstring'] == 'Test class docstring'
        assert len(test_class['methods']) == 2  # __init__ and method_with_complexity

        # 验证函数分析
        assert len(analysis['functions']) == 1
        func = analysis['functions'][0]
        assert func['name'] == 'standalone_function'
        assert func['docstring'] == 'Standalone function'

        # 验证导入分析
        assert len(analysis['imports']) == 3  # os, Dict, List

        # 验证复杂度
        assert analysis['complexity'] > 0

    def test_analyze_code_dependencies(self):
        """测试代码依赖关系分析"""
        test_files = ["dep_test1.py", "dep_test2.py"]

        # 创建第一个文件
        with open(self.project_root / test_files[0], 'w') as f:
            f.write("import os\nfrom workflow.models import CodeGenerationResult\nimport requests\n")

        # 创建第二个文件
        with open(self.project_root / test_files[1], 'w') as f:
            f.write("import json\nfrom typing import Dict\nimport numpy as np\n")

        analysis = self.generator.analyze_code_dependencies(test_files)

        assert 'dependency_graph' in analysis
        assert 'all_dependencies' in analysis
        assert 'internal_dependencies' in analysis
        assert 'external_dependencies' in analysis

        # 验证依赖图
        assert test_files[0] in analysis['dependency_graph']
        assert test_files[1] in analysis['dependency_graph']

        # 验证内部和外部依赖分类
        assert 'workflow.models' in analysis['internal_dependencies']
        # os 和 json 是标准库，应该不在外部依赖中
        # requests 和 numpy 是外部依赖
        assert 'requests' in analysis['external_dependencies'] or 'numpy' in analysis['external_dependencies']

    def test_suggest_code_improvements(self):
        """测试代码改进建议"""
        test_file = "improvement_test.py"
        test_content = '''
def complex_function(x):  # 缺少类型提示和文档字符串
    if x > 0:
        if x > 10:
            if x > 20:
                if x > 30:
                    if x > 40:
                        return "very high"
                    return "high"
                return "medium high"
            return "medium"
        return "low"
    return "zero or negative"

class UndocumentedClass:  # 缺少文档字符串
    def undocumented_method(self):  # 缺少文档字符串
        pass
'''

        full_path = self.project_root / test_file
        with open(full_path, 'w') as f:
            f.write(test_content)

        suggestions = self.generator.suggest_code_improvements(test_file)

        assert len(suggestions) > 0

        # 应该有复杂度相关的建议
        complexity_suggestions = [s for s in suggestions if s['type'] in ['complexity', 'function_complexity']]
        assert len(complexity_suggestions) > 0

        # 应该有文档相关的建议
        doc_suggestions = [s for s in suggestions if s['type'] == 'documentation']
        assert len(doc_suggestions) > 0

    def test_calculate_complexity_advanced(self):
        """测试高级复杂度计算"""
        test_content = '''
def complex_function():
    if True:  # +1
        for i in range(10):  # +1
            while i > 0:  # +1
                try:  # +1
                    if i > 5 and i < 8:  # +1 (and adds complexity)
                        pass
                except ValueError:  # +1 (each except handler)
                    pass
                except TypeError:  # +1
                    pass
                with open("file") as f:  # +1
                    pass
'''

        import ast
        tree = ast.parse(test_content)
        complexity = self.generator._calculate_complexity(tree)

        # 基础复杂度 1 + 各种控制结构
        assert complexity > 5

    def test_is_external_dependency(self):
        """测试外部依赖判断"""
        # 标准库模块
        assert self.generator._is_external_dependency('os') is False
        assert self.generator._is_external_dependency('json') is False
        assert self.generator._is_external_dependency('datetime') is False

        # 项目内部模块
        assert self.generator._is_external_dependency('workflow.models') is False
        assert self.generator._is_external_dependency('.models') is False

        # 外部依赖
        assert self.generator._is_external_dependency('requests') is True
        assert self.generator._is_external_dependency('numpy') is True
        assert self.generator._is_external_dependency('fastmcp') is True


class TestCodeGenerationResult:
    """测试 CodeGenerationResult 数据模型"""

    def test_init(self):
        """测试初始化"""
        result = CodeGenerationResult(
            success=True,
            generated_files=["test.py"],
            modified_files=["existing.py"],
            deleted_files=["old.py"],
            operation_type="create",
            quality_metrics={"complexity": 5},
            validation_results={"syntax_valid": True},
            errors=[],
            warnings=["warning message"],
            execution_time=1.5
        )

        assert result.success is True
        assert result.generated_files == ["test.py"]
        assert result.modified_files == ["existing.py"]
        assert result.deleted_files == ["old.py"]
        assert result.operation_type == "create"
        assert result.quality_metrics == {"complexity": 5}
        assert result.validation_results == {"syntax_valid": True}
        assert result.errors == []
        assert result.warnings == ["warning message"]
        assert result.execution_time == 1.5
        assert isinstance(result.created_at, datetime)

    def test_to_dict(self):
        """测试序列化为字典"""
        result = CodeGenerationResult(
            success=True,
            generated_files=["test.py"],
            modified_files=[],
            deleted_files=[],
            operation_type="create",
            quality_metrics={},
            validation_results={},
            errors=[],
            warnings=[],
            execution_time=1.0
        )

        data = result.to_dict()

        assert isinstance(data, dict)
        assert data['success'] is True
        assert data['generated_files'] == ["test.py"]
        assert 'created_at' in data
        assert isinstance(data['created_at'], str)
        assert data['created_at'].endswith('Z')

    def test_from_dict(self):
        """测试从字典反序列化"""
        data = {
            'success': True,
            'generated_files': ["test.py"],
            'modified_files': [],
            'deleted_files': [],
            'operation_type': "create",
            'quality_metrics': {},
            'validation_results': {},
            'errors': [],
            'warnings': [],
            'execution_time': 1.0,
            'created_at': '2023-01-01T00:00:00Z'
        }

        result = CodeGenerationResult.from_dict(data)

        assert result.success is True
        assert result.generated_files == ["test.py"]
        assert isinstance(result.created_at, datetime)

    def test_to_json(self):
        """测试序列化为 JSON"""
        result = CodeGenerationResult(
            success=True,
            generated_files=["test.py"],
            modified_files=[],
            deleted_files=[],
            operation_type="create",
            quality_metrics={},
            validation_results={},
            errors=[],
            warnings=[],
            execution_time=1.0
        )

        json_str = result.to_json()

        assert isinstance(json_str, str)
        assert '"success": true' in json_str
        assert '"generated_files": [\n    "test.py"\n  ]' in json_str

    def test_from_json(self):
        """测试从 JSON 反序列化"""
        json_str = '''
        {
          "success": true,
          "generated_files": ["test.py"],
          "modified_files": [],
          "deleted_files": [],
          "operation_type": "create",
          "quality_metrics": {},
          "validation_results": {},
          "errors": [],
          "warnings": [],
          "execution_time": 1.0,
          "created_at": "2023-01-01T00:00:00Z"
        }
        '''

        result = CodeGenerationResult.from_json(json_str)

        assert result.success is True
        assert result.generated_files == ["test.py"]
        assert isinstance(result.created_at, datetime)

    def test_save_and_load_file(self):
        """测试文件保存和加载"""
        import tempfile
        import os

        result = CodeGenerationResult(
            success=True,
            generated_files=["test.py"],
            modified_files=[],
            deleted_files=[],
            operation_type="create",
            quality_metrics={"complexity": 3},
            validation_results={"syntax_valid": True},
            errors=[],
            warnings=["test warning"],
            execution_time=2.5
        )

        # 保存到临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_file = f.name

        try:
            result.save_to_file(temp_file)

            # 验证文件存在
            assert os.path.exists(temp_file)

            # 加载并验证
            loaded_result = CodeGenerationResult.load_from_file(temp_file)

            assert loaded_result.success == result.success
            assert loaded_result.generated_files == result.generated_files
            assert loaded_result.quality_metrics == result.quality_metrics
            assert loaded_result.validation_results == result.validation_results
            assert loaded_result.warnings == result.warnings
            assert loaded_result.execution_time == result.execution_time

        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)


class TestAgentSession:
    """测试 AgentSession 数据模型"""

    def test_init(self):
        """测试初始化"""
        session = AgentSession(
            session_id="test_session",
            agent_id="test_agent",
            status="active",
            current_task=None
        )

        assert session.session_id == "test_session"
        assert session.agent_id == "test_agent"
        assert session.status == AgentStatus.ACTIVE
        assert isinstance(session.created_at, datetime)
        assert session.context == {}
        assert session.current_task is None

    def test_to_dict(self):
        """测试序列化为字典"""
        session = AgentSession(
            session_id="test_session",
            agent_id="test_agent",
            status="active",
            current_task="task_123",
            context={"key": "value"}
        )

        data = session.to_dict()

        assert data["session_id"] == "test_session"
        assert data["agent_id"] == "test_agent"
        assert data["status"] == "active"
        assert data["current_task"] == "task_123"
        assert data["context"] == {"key": "value"}
        assert data["created_at"].endswith("Z")


class TestTaskExecution:
    """测试 TaskExecution 数据模型"""

    def test_init(self):
        """测试初始化"""
        task = TaskExecution(
            task_id="test_task",
            story_id="test_story",
            agent_id="test_agent",
            task_type="code_generation",
            status="pending"
        )

        assert task.task_id == "test_task"
        assert task.story_id == "test_story"
        assert task.agent_id == "test_agent"
        assert task.task_type == "code_generation"
        assert task.status == "pending"
        assert isinstance(task.created_at, str)  # created_at 是 ISO 字符串格式
        assert task.execution_time is None

    def test_complete_task(self):
        """测试任务完成"""
        task = TaskExecution(
            task_id="test_task",
            story_id="test_story",
            agent_id="test_agent",
            task_type="code_generation",
            status="running"
        )

        # 模拟任务完成
        task.status = "completed"
        task.execution_time = 5.0
        task.output_result = {"result": "success"}

        assert task.status == "completed"
        assert task.execution_time == 5.0
        assert task.output_result == {"result": "success"}

    def test_to_dict(self):
        """测试序列化为字典"""
        task = TaskExecution(
            task_id="test_task",
            story_id="test_story",
            agent_id="test_agent",
            task_type="code_generation",
            status="completed",
            execution_time=3.5
        )

        data = task.to_dict()

        assert data["task_id"] == "test_task"
        assert data["story_id"] == "test_story"
        assert data["agent_id"] == "test_agent"
        assert data["task_type"] == "code_generation"
        assert data["status"] == "completed"
        assert data["execution_time"] == 3.5
        assert data["created_at"].endswith("Z")


class TestSharedContext:
    """测试 SharedContext 数据模型"""

    def test_init(self):
        """测试初始化"""
        context = SharedContext(
            context_id="test_context",
            agent_id="test_agent",
            task_type="analysis",
            document_fragments={"file1.py": "content1"},
            metadata={"version": "1.0"},
            version=1
        )

        assert context.context_id == "test_context"
        assert context.agent_id == "test_agent"
        assert context.task_type == "analysis"
        assert context.document_fragments == {"file1.py": "content1"}
        assert context.metadata == {"version": "1.0"}
        assert context.version == 1
        assert context.access_count == 0

    def test_touch(self):
        """测试访问计数"""
        context = SharedContext(
            context_id="test_context",
            agent_id="test_agent",
            task_type="analysis",
            document_fragments={},
            metadata={},
            version=1
        )

        initial_count = context.access_count
        initial_time = context.last_accessed

        # 等待一小段时间确保时间戳不同
        import time
        time.sleep(0.01)

        context.touch()

        assert context.access_count == initial_count + 1
        assert context.last_accessed > initial_time

    def test_to_dict(self):
        """测试序列化为字典"""
        context = SharedContext(
            context_id="test_context",
            agent_id="test_agent",
            task_type="analysis",
            document_fragments={"file1.py": "content1"},
            metadata={"key": "value"},
            version=1
        )

        data = context.to_dict()

        assert data["context_id"] == "test_context"
        assert data["agent_id"] == "test_agent"
        assert data["document_fragments"] == {"file1.py": "content1"}
        assert data["metadata"] == {"key": "value"}
        assert data["version"] == 1
        assert data["created_at"].endswith("Z")
        assert data["updated_at"].endswith("Z")
        assert data["last_accessed"].endswith("Z")


class TestContextCache:
    """测试 ContextCache 数据模型"""

    def test_init(self):
        """测试初始化"""
        cache = ContextCache(
            cache_key="test_key",
            content="test_content",
            size_bytes=1024
        )

        assert cache.cache_key == "test_key"
        assert cache.content == "test_content"
        assert cache.size_bytes == 1024
        assert cache.hit_count == 0
        assert isinstance(cache.created_at, datetime)
        assert isinstance(cache.last_accessed, datetime)
        assert cache.expiry_time is None

    def test_touch(self):
        """测试缓存访问"""
        cache = ContextCache(
            cache_key="test_key",
            content="test_content",
            size_bytes=1024
        )

        initial_count = cache.hit_count
        initial_time = cache.last_accessed

        # 等待一小段时间确保时间戳不同
        import time
        time.sleep(0.01)

        cache.touch()

        assert cache.hit_count == initial_count + 1
        assert cache.last_accessed > initial_time

    def test_with_expiry(self):
        """测试带过期时间的缓存"""
        expiry = datetime.utcnow()
        cache = ContextCache(
            cache_key="test_key",
            content="test_content",
            size_bytes=1024,
            expiry_time=expiry
        )

        assert cache.expiry_time == expiry


class TestWorkflowExecution:
    """测试 WorkflowExecution 数据模型"""

    def test_init(self):
        """测试初始化"""
        workflow = WorkflowExecution(
            execution_id="test_execution",
            workflow_type="greenfield",
            current_phase="document_processing",
            total_phases=4,
            completed_phases=1
        )

        assert workflow.execution_id == "test_execution"
        assert workflow.workflow_type == "greenfield"
        assert workflow.current_phase == "document_processing"
        assert workflow.total_phases == 4
        assert workflow.completed_phases == 1
        assert workflow.status == "running"
        assert workflow.phase_progress == {}
        assert workflow.user_confirmations == []
        assert workflow.checkpoints == []
        assert workflow.started_at.endswith("Z")

    def test_to_dict(self):
        """测试序列化为字典"""
        workflow = WorkflowExecution(
            execution_id="test_execution",
            workflow_type="brownfield",
            current_phase="epic_creation",
            total_phases=3,
            completed_phases=0,
            phase_progress={"document_processing": 1.0},
            status="paused"
        )

        data = workflow.to_dict()

        assert data["execution_id"] == "test_execution"
        assert data["workflow_type"] == "brownfield"
        assert data["current_phase"] == "epic_creation"
        assert data["total_phases"] == 3
        assert data["completed_phases"] == 0
        assert data["phase_progress"] == {"document_processing": 1.0}
        assert data["status"] == "paused"
        assert data["started_at"].endswith("Z")

    def test_touch(self):
        """测试 touch 方法"""
        workflow = WorkflowExecution(
            execution_id="test_execution",
            workflow_type="greenfield",
            current_phase="implementation"
        )

        original_started_at = workflow.started_at
        workflow.touch()

        # touch 方法应该保持 started_at 不变（如果已经设置）
        assert workflow.started_at == original_started_at

    def test_workflow_progress(self):
        """测试工作流程进度管理"""
        workflow = WorkflowExecution(
            execution_id="test_execution",
            workflow_type="greenfield",
            current_phase="story_development",
            total_phases=4,
            completed_phases=2,
            phase_progress={
                "document_processing": 1.0,
                "epic_creation": 1.0,
                "story_development": 0.5
            }
        )

        # 计算总体进度
        total_progress = sum(workflow.phase_progress.values()) / workflow.total_phases
        assert total_progress == 0.625  # (1.0 + 1.0 + 0.5) / 4

        # 添加用户确认
        workflow.user_confirmations.append({
            "phase": "story_development",
            "decision": "approved",
            "timestamp": "2025-01-12T10:00:00Z"
        })

        assert len(workflow.user_confirmations) == 1
        assert workflow.user_confirmations[0]["decision"] == "approved"


class TestUserInteraction:
    """测试 UserInteraction 数据模型"""

    def test_init(self):
        """测试初始化"""
        interaction = UserInteraction(
            interaction_id="test_interaction",
            phase="epic_creation",
            message="Please confirm the epic structure",
            options=["approve", "modify", "reject"]
        )

        assert interaction.interaction_id == "test_interaction"
        assert interaction.phase == "epic_creation"
        assert interaction.message == "Please confirm the epic structure"
        assert interaction.options == ["approve", "modify", "reject"]
        assert interaction.user_choice is None
        assert interaction.timestamp.endswith("Z")
        assert interaction.auto_proceed is False

    def test_to_dict(self):
        """测试序列化为字典"""
        interaction = UserInteraction(
            interaction_id="test_interaction",
            phase="implementation",
            message="Code review completed",
            options=["deploy", "revise"],
            user_choice="deploy",
            auto_proceed=True
        )

        data = interaction.to_dict()

        assert data["interaction_id"] == "test_interaction"
        assert data["phase"] == "implementation"
        assert data["message"] == "Code review completed"
        assert data["options"] == ["deploy", "revise"]
        assert data["user_choice"] == "deploy"
        assert data["auto_proceed"] is True
        assert data["timestamp"].endswith("Z")

    def test_user_choice_workflow(self):
        """测试用户选择工作流程"""
        interaction = UserInteraction(
            interaction_id="choice_test",
            phase="story_development",
            message="Select story priority",
            options=["high", "medium", "low"]
        )

        # 初始状态
        assert interaction.user_choice is None

        # 用户做出选择
        interaction.user_choice = "high"

        assert interaction.user_choice == "high"
        assert interaction.user_choice in interaction.options

    def test_auto_proceed_interaction(self):
        """测试自动进行的交互"""
        interaction = UserInteraction(
            interaction_id="auto_test",
            phase="document_processing",
            message="Document analysis completed",
            options=["continue"],
            auto_proceed=True
        )

        assert interaction.auto_proceed is True

        # 自动进行的交互可以自动设置选择
        if interaction.auto_proceed and len(interaction.options) == 1:
            interaction.user_choice = interaction.options[0]

        assert interaction.user_choice == "continue"
