"""
测试模板管理器
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from workflow.template_manager import TemplateManager


class TestTemplateManager:
    """测试 TemplateManager 类"""
    
    def setup_method(self):
        """设置测试环境"""
        self.temp_dir = tempfile.mkdtemp()
        self.template_root = Path(self.temp_dir)
        
        # 创建模板目录结构
        python_dir = self.template_root / "python"
        python_dir.mkdir(parents=True)
        
        # 创建测试模板
        test_template = """class {{ class_name }}:
    \"\"\"{{ description }}\"\"\"
    
    def __init__(self):
        pass
        
    {% for method in methods %}
    def {{ method }}(self):
        pass
    {% endfor %}"""
        
        with open(python_dir / "class.j2", 'w') as f:
            f.write(test_template)
        
        # 创建测试配置
        test_config = """templates:
  class:
    description: "测试类模板"
    parameters:
      class_name:
        type: string
        required: true
        description: "类名称"
      description:
        type: string
        required: true
        description: "类描述"
      methods:
        type: array
        required: false
        description: "方法列表"
    default_parameters:
      class_name: "TestClass"
      description: "测试类"
      methods: []"""
        
        with open(python_dir / "templates.yaml", 'w') as f:
            f.write(test_config)
        
        self.manager = TemplateManager(str(self.template_root))
    
    def teardown_method(self):
        """清理测试环境"""
        shutil.rmtree(self.temp_dir)
    
    def test_init(self):
        """测试初始化"""
        assert self.manager.template_root == self.template_root
        assert self.manager.jinja_env is not None
        assert len(self.manager.template_configs) > 0
        assert 'class' in self.manager.template_configs
    
    def test_get_available_templates(self):
        """测试获取可用模板"""
        templates = self.manager.get_available_templates()
        
        assert len(templates) > 0
        
        # 查找 class 模板
        class_template = next((t for t in templates if t['name'] == 'class'), None)
        assert class_template is not None
        assert class_template['language'] == 'python'
        assert 'config' in class_template
    
    def test_get_available_templates_with_language_filter(self):
        """测试按语言过滤模板"""
        templates = self.manager.get_available_templates(language='python')
        
        assert len(templates) > 0
        for template in templates:
            assert template['language'] == 'python'
        
        # 测试不存在的语言
        templates = self.manager.get_available_templates(language='nonexistent')
        assert len(templates) == 0
    
    def test_render_template(self):
        """测试模板渲染"""
        parameters = {
            'class_name': 'MyClass',
            'description': '我的测试类',
            'methods': ['process', 'validate']
        }
        
        result = self.manager.render_template('class', 'python', parameters)
        
        assert 'class MyClass:' in result
        assert '我的测试类' in result
        assert 'def process(self):' in result
        assert 'def validate(self):' in result
    
    def test_render_template_with_defaults(self):
        """测试使用默认参数渲染模板"""
        result = self.manager.render_template('class', 'python', {})
        
        assert 'class TestClass:' in result
        assert '测试类' in result
    
    def test_render_template_not_found(self):
        """测试渲染不存在的模板"""
        with pytest.raises(Exception):
            self.manager.render_template('nonexistent', 'python', {})
    
    def test_create_template_from_code(self):
        """测试从代码创建模板"""
        code = '''class TestClass:
    """Test docstring"""
    
    def test_function(self):
        # Test comment
        pass'''
        
        result = self.manager.create_template_from_code(code, 'new_template', 'python')
        
        assert result is True
        
        # 验证模板文件是否创建
        template_file = self.template_root / 'python' / 'new_template.j2'
        assert template_file.exists()
        
        # 验证模板内容
        with open(template_file, 'r') as f:
            content = f.read()
        assert '{{ class_name }}' in content or 'TestClass' in content
    
    def test_validate_template_valid(self):
        """测试验证有效模板"""
        result = self.manager.validate_template('class', 'python')
        
        assert result['valid'] is True
        assert len(result['errors']) == 0
    
    def test_validate_template_not_found(self):
        """测试验证不存在的模板"""
        result = self.manager.validate_template('nonexistent', 'python')
        
        assert result['valid'] is False
        assert len(result['errors']) > 0
        assert 'not found' in result['errors'][0]
    
    def test_get_template_parameters(self):
        """测试获取模板参数"""
        params = self.manager.get_template_parameters('class', 'python')
        
        assert 'parameters' in params
        assert 'default_parameters' in params
        assert 'description' in params
        
        # 验证参数信息
        assert 'class_name' in params['parameters']
        assert 'description' in params['parameters']
        assert 'methods' in params['parameters']
        
        # 验证默认参数
        assert params['default_parameters']['class_name'] == 'TestClass'
    
    def test_get_template_parameters_not_found(self):
        """测试获取不存在模板的参数"""
        params = self.manager.get_template_parameters('nonexistent', 'python')
        
        assert params['parameters'] == {}
        assert params['default_parameters'] == {}
        assert params['description'] == ''
    
    def test_create_custom_template(self):
        """测试创建自定义模板"""
        template_content = '''def {{ function_name }}():
    """{{ description }}"""
    pass'''
        
        config = {
            'description': '自定义函数模板',
            'parameters': {
                'function_name': {
                    'type': 'string',
                    'required': True,
                    'description': '函数名称'
                },
                'description': {
                    'type': 'string',
                    'required': True,
                    'description': '函数描述'
                }
            },
            'default_parameters': {
                'function_name': 'custom_function',
                'description': '自定义函数'
            }
        }
        
        result = self.manager.create_custom_template('custom_func', 'python', template_content, config)
        
        assert result is True
        
        # 验证模板文件
        template_file = self.template_root / 'python' / 'custom_func.j2'
        assert template_file.exists()
        
        # 验证配置文件
        config_file = self.template_root / 'python' / 'custom_func.yaml'
        assert config_file.exists()
        
        # 验证内存中的配置
        assert 'custom_func' in self.manager.template_configs
        assert self.manager.template_configs['custom_func']['description'] == '自定义函数模板'
    
    def test_create_custom_template_render(self):
        """测试创建的自定义模板能够正确渲染"""
        template_content = '''def {{ function_name }}():
    """{{ description }}"""
    return "{{ return_value }}"'''
        
        config = {
            'description': '自定义模板',
            'default_parameters': {
                'function_name': 'test_func',
                'description': '测试函数',
                'return_value': 'success'
            }
        }
        
        # 创建模板
        self.manager.create_custom_template('test_template', 'python', template_content, config)
        
        # 渲染模板
        result = self.manager.render_template('test_template', 'python', {
            'function_name': 'my_function',
            'description': '我的函数',
            'return_value': 'result'
        })
        
        assert 'def my_function():' in result
        assert '我的函数' in result
        assert 'return "result"' in result
    
    def test_convert_code_to_template(self):
        """测试代码转模板功能"""
        code = '''class TestClass:
    """Test docstring"""
    
    def test_function(self):
        # Test comment
        pass'''
        
        template_content = self.manager._convert_code_to_template(code)
        
        # 验证是否进行了基本的模板化
        assert isinstance(template_content, str)
        assert len(template_content) > 0
