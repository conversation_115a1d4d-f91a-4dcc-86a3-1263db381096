"""
测试智能体编排功能
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from workflow.models import AgentSession, TaskExecution, AgentStatus
from workflow.agent_orchestrator import AgentOrchestrator


class TestAgentOrchestratorIntegration:
    """测试智能体编排集成功能"""

    def setup_method(self):
        """设置测试环境"""
        self.orchestrator = AgentOrchestrator()

    @pytest.mark.asyncio
    async def test_activate_agent_integration(self):
        """测试激活智能体集成功能"""
        session = await self.orchestrator.activate_agent("test_agent", {"key": "value"})

        assert isinstance(session, AgentSession)
        assert session.agent_id == "test_agent"
        assert session.status == AgentStatus.ACTIVE
        assert session.context == {"key": "value"}
        assert session.session_id in self.orchestrator.active_sessions
    
    @pytest.mark.asyncio
    async def test_assign_task_integration(self):
        """测试任务分配集成功能"""
        task = TaskExecution(
            task_id="test_task",
            story_id="test_story",
            agent_id=None,
            task_type="code_generation",
            status="pending"
        )

        result_task = await self.orchestrator.assign_task("test_agent", task)

        assert result_task.agent_id == "test_agent"
        assert result_task.status == "assigned"
        assert result_task.session_id is not None
        # 验证任务被添加到队列
        assert result_task in self.orchestrator.task_queue

    @pytest.mark.asyncio
    async def test_transfer_context_integration(self):
        """测试上下文转移集成功能"""
        context = {"key": "value", "data": "test_data"}

        result = await self.orchestrator.transfer_context("agent_a", "agent_b", context)

        assert result is True
        assert len(self.orchestrator.shared_contexts) > 0

        # 验证共享上下文
        shared_context = list(self.orchestrator.shared_contexts.values())[0]
        assert shared_context.agent_id == "agent_a"
        assert shared_context.task_type == "context_transfer"
        assert shared_context.metadata == context

    @pytest.mark.asyncio
    async def test_agent_status_integration(self):
        """测试智能体状态集成功能"""
        # 初始状态应该是 inactive
        status = await self.orchestrator.get_agent_status("test_agent")
        assert status == "inactive"

        # 激活智能体
        await self.orchestrator.activate_agent("test_agent")

        # 现在状态应该是 active
        status = await self.orchestrator.get_agent_status("test_agent")
        assert status == "active"

        # 健康检查
        health = await self.orchestrator.health_check_agent("test_agent")
        assert health["status"] == "healthy"
        assert health["session_id"] is not None
        assert health["uptime"] >= 0

    @pytest.mark.asyncio
    async def test_complete_workflow_integration(self):
        """测试完整的工作流程集成"""
        # 1. 设置工作流程自动化
        await self.orchestrator.setup_workflow_automation()
        assert self.orchestrator.workflow_automation_setup is True

        # 2. 触发工作流程
        await self.orchestrator.trigger_workflow("story_123", {"context": "test"})

        # 验证工作流程任务被创建
        workflow_tasks = [t for t in self.orchestrator.task_queue if t.task_type == "workflow"]
        assert len(workflow_tasks) > 0
        assert workflow_tasks[0].story_id == "story_123"

        # 3. 激活智能体
        session = await self.orchestrator.activate_agent("dev_agent", {"role": "developer"})
        assert session.agent_id == "dev_agent"
        assert session.context["role"] == "developer"

        # 4. 分配任务
        task = TaskExecution(
            task_id="dev_task_123",
            story_id="story_123",
            agent_id=None,
            task_type="code_generation",
            status="pending"
        )

        assigned_task = await self.orchestrator.assign_task("dev_agent", task)
        assert assigned_task.agent_id == "dev_agent"
        assert assigned_task.session_id == session.session_id

        # 5. 完成任务
        completed_task = await self.orchestrator.record_task_complete(
            assigned_task,
            {"result": "success", "files_generated": ["test.py"]}
        )

        assert completed_task.status == "completed"
        assert completed_task.output_context["result"] == "success"
        assert completed_task.execution_time is not None

        # 6. 生成状态报告
        report = await self.orchestrator.generate_agent_status_report()
        assert report["total_agents"] >= 1
        assert report["active_agents"] >= 1
        assert len(report["agents"]) >= 1
        assert report["total_tasks"] >= 2  # workflow task + dev task
    
    @pytest.mark.asyncio
    async def test_start_agent_workflow_error(self, mock_agent_orchestrator):
        """测试启动智能体工作流程失败"""
        mock_agent_orchestrator.setup_workflow_automation.side_effect = Exception("Setup failed")
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import start_agent_workflow
            
            result = await start_agent_workflow("story_123")
            
            assert result["success"] is False
            assert "Failed to start workflow" in result["error"]
    
    @pytest.mark.asyncio
    async def test_transfer_task_to_agent_success(self, mock_agent_orchestrator):
        """测试任务转移成功"""
        # 模拟返回的任务
        mock_task = TaskExecution(
            task_id="task_123",
            story_id="story_123",
            agent_id="agent_b",
            task_type="transfer",
            status="pending",
            session_id="session_456"
        )
        mock_agent_orchestrator.assign_task.return_value = mock_task
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import transfer_task_to_agent
            
            task_data = {
                "task_id": "task_123",
                "story_id": "story_123",
                "task_type": "code_generation",
                "context": {"key": "value"}
            }
            
            result = await transfer_task_to_agent("agent_a", "agent_b", task_data)
            
            assert result["success"] is True
            assert result["task_id"] == "task_123"
            assert result["from_agent"] == "agent_a"
            assert result["to_agent"] == "agent_b"
            assert result["session_id"] == "session_456"
            
            mock_agent_orchestrator.transfer_context.assert_called_once_with(
                "agent_a", "agent_b", {"key": "value"}
            )
            mock_agent_orchestrator.assign_task.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_transfer_task_context_failure(self, mock_agent_orchestrator):
        """测试任务转移时上下文传输失败"""
        mock_agent_orchestrator.transfer_context.return_value = False
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import transfer_task_to_agent
            
            task_data = {"task_id": "task_123", "context": {}}
            
            result = await transfer_task_to_agent("agent_a", "agent_b", task_data)
            
            assert result["success"] is False
            assert "Failed to transfer context" in result["error"]
    
    @pytest.mark.asyncio
    async def test_get_agent_orchestrator_status_success(self, mock_agent_orchestrator):
        """测试获取智能体状态成功"""
        mock_agent_orchestrator.get_agent_status.return_value = "active"
        mock_agent_orchestrator.health_check_agent.return_value = {"status": "healthy"}
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import get_agent_orchestrator_status
            
            result = await get_agent_orchestrator_status("agent_123")
            
            assert result["success"] is True
            assert result["agent_id"] == "agent_123"
            assert result["status"] == "active"
            assert result["health"] == {"status": "healthy"}
            assert "timestamp" in result
    
    @pytest.mark.asyncio
    async def test_get_all_agents_orchestrator_status_success(self, mock_agent_orchestrator):
        """测试获取所有智能体状态成功"""
        mock_report = {
            "total_agents": 3,
            "active_agents": 2,
            "agents": [
                {"agent_id": "agent_1", "status": "active"},
                {"agent_id": "agent_2", "status": "active"},
                {"agent_id": "agent_3", "status": "inactive"}
            ]
        }
        mock_agent_orchestrator.generate_agent_status_report.return_value = mock_report
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import get_all_agents_orchestrator_status
            
            result = await get_all_agents_orchestrator_status()
            
            assert result["success"] is True
            assert result["report"] == mock_report
    
    @pytest.mark.asyncio
    async def test_activate_agent_session_success(self, mock_agent_orchestrator):
        """测试激活智能体会话成功"""
        mock_session = AgentSession(
            session_id="session_123",
            agent_id="agent_123",
            status="active"
        )
        mock_agent_orchestrator.activate_agent.return_value = mock_session
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import activate_agent_session
            
            result = await activate_agent_session("agent_123", {"key": "value"})
            
            assert result["success"] is True
            assert result["session"]["session_id"] == "session_123"
            assert result["session"]["agent_id"] == "agent_123"
            assert result["session"]["status"] == "active"
            assert result["session"]["created_at"].endswith("Z")
    
    @pytest.mark.asyncio
    async def test_complete_agent_task_success(self, mock_agent_orchestrator):
        """测试完成智能体任务成功"""
        mock_completed_task = TaskExecution(
            task_id="task_123",
            story_id="story_123",
            agent_id="agent_123",
            task_type="code_generation",
            status="completed",
            execution_time=5.5
        )
        mock_agent_orchestrator.record_task_complete.return_value = mock_completed_task
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import complete_agent_task
            
            task_result = {
                "story_id": "story_123",
                "task_type": "code_generation",
                "output": "success"
            }
            
            result = await complete_agent_task("task_123", "agent_123", task_result)
            
            assert result["success"] is True
            assert result["task_id"] == "task_123"
            assert result["agent_id"] == "agent_123"
            assert result["execution_time"] == 5.5
            assert "marked as completed" in result["message"]
    
    @pytest.mark.asyncio
    async def test_complete_agent_task_error(self, mock_agent_orchestrator):
        """测试完成智能体任务失败"""
        mock_agent_orchestrator.record_task_complete.side_effect = Exception("Task completion failed")
        
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import complete_agent_task
            
            result = await complete_agent_task("task_123", "agent_123")
            
            assert result["success"] is False
            assert "Failed to complete task" in result["error"]
    
    @pytest.mark.asyncio
    async def test_integration_workflow(self, mock_agent_orchestrator):
        """测试完整的智能体编排工作流程"""
        # 1. 启动工作流程
        with patch('bmad_agent_mcp.agent_orchestrator', mock_agent_orchestrator):
            from bmad_agent_mcp import (
                start_agent_workflow, 
                activate_agent_session,
                transfer_task_to_agent,
                complete_agent_task
            )
            
            # 启动工作流程
            workflow_result = await start_agent_workflow("story_123")
            assert workflow_result["success"] is True
            
            # 激活智能体会话
            mock_session = AgentSession(
                session_id="session_123",
                agent_id="agent_123",
                status="active"
            )
            mock_agent_orchestrator.activate_agent.return_value = mock_session
            
            session_result = await activate_agent_session("agent_123")
            assert session_result["success"] is True
            
            # 转移任务
            mock_task = TaskExecution(
                task_id="task_123",
                story_id="story_123",
                agent_id="agent_b",
                task_type="transfer",
                status="pending",
                session_id="session_456"
            )
            mock_agent_orchestrator.assign_task.return_value = mock_task
            
            task_data = {"task_id": "task_123", "context": {}}
            transfer_result = await transfer_task_to_agent("agent_a", "agent_b", task_data)
            assert transfer_result["success"] is True
            
            # 完成任务
            mock_completed_task = TaskExecution(
                task_id="task_123",
                story_id="story_123",
                agent_id="agent_b",
                task_type="transfer",
                status="completed",
                execution_time=3.0
            )
            mock_agent_orchestrator.record_task_complete.return_value = mock_completed_task
            
            complete_result = await complete_agent_task("task_123", "agent_b")
            assert complete_result["success"] is True
