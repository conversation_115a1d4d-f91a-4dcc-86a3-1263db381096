#!/usr/bin/env python3\n\"\"\"\n简化的 BMAD Agent FastMCP Service 测试\n\n直接测试核心功能，不依赖 FastMCP 的内部 API\n\"\"\"\n\nimport sys\nfrom pathlib import Path\n\ndef test_bmad_core():\n    \"\"\"测试 BMAD 核心功能\"\"\"\n    print(\"🧪 测试 BMAD 核心功能\")\n    print(\"-\" * 30)\n    \n    try:\n        from bmad_agent_mcp import bmad_core\n        \n        print(f\"✅ BMAD 核心加载成功\")\n        print(f\"   智能体数量: {len(bmad_core.agents)}\")\n        print(f\"   工作流程数量: {len(bmad_core.workflows)}\")\n        print(f\"   任务数量: {len(bmad_core.tasks)}\")\n        print(f\"   模板数量: {len(bmad_core.templates)}\")\n        \n        # 列出智能体\n        if bmad_core.agents:\n            print(f\"\\n🤖 发现的智能体:\")\n            for agent_id, agent in list(bmad_core.agents.items())[:5]:\n                print(f\"   - {agent_id}: {agent.title} {agent.icon}\")\n            if len(bmad_core.agents) > 5:\n                print(f\"   ... 还有 {len(bmad_core.agents) - 5} 个智能体\")\n        \n        # 列出工作流程\n        if bmad_core.workflows:\n            print(f\"\\n📋 发现的工作流程:\")\n            for workflow_id, workflow in list(bmad_core.workflows.items())[:3]:\n                print(f\"   - {workflow_id}: {workflow.name}\")\n            if len(bmad_core.workflows) > 3:\n                print(f\"   ... 还有 {len(bmad_core.workflows) - 3} 个工作流程\")\n        \n        return True\n        \n    except Exception as e:\n        print(f\"❌ BMAD 核心加载失败: {e}\")\n        return False\n\ndef test_llm_client():\n    \"\"\"测试 LLM 客户端\"\"\"\n    print(\"\\n🧪 测试 LLM 客户端\")\n    print(\"-\" * 30)\n    \n    try:\n        from llm_client import LLMClient\n        \n        client = LLMClient()\n        print(f\"✅ LLM 客户端创建成功\")\n        \n        # 获取模式信息\n        mode_info = client.get_mode_info()\n        print(f\"   当前模式: {mode_info['mode']}\")\n        print(f\"   状态: {mode_info['status']}\")\n        \n        # 测试模式切换\n        print(f\"\\n🔄 测试模式切换:\")\n        \n        # 切换到内置模式\n        result = client.switch_mode('builtin')\n        print(f\"   切换到内置模式: {result['success']}\")\n        \n        # 切换到外部模式（如果有 API Key）\n        result = client.switch_mode('external')\n        if result['success']:\n            print(f\"   切换到外部模式: {result['success']}\")\n        else:\n            print(f\"   外部模式不可用: {result.get('error', '未知错误')}\")\n        \n        # 切换回内置模式\n        client.switch_mode('builtin')\n        print(f\"   切换回内置模式: ✅\")\n        \n        return True\n        \n    except Exception as e:\n        print(f\"❌ LLM 客户端测试失败: {e}\")\n        return False\n\ndef test_utils():\n    \"\"\"测试工具函数\"\"\"\n    print(\"\\n🧪 测试工具函数\")\n    print(\"-\" * 30)\n    \n    try:\n        from utils import (\n            validate_bmad_core_structure,\n            get_agent_info,\n            get_workflow_info,\n            format_agent_response\n        )\n        \n        print(f\"✅ 工具函数导入成功\")\n        \n        # 验证 BMAD 核心结构\n        validation_result = validate_bmad_core_structure()\n        print(f\"   BMAD 结构验证: {'✅' if validation_result['valid'] else '❌'}\")\n        \n        if not validation_result['valid']:\n            print(f\"   验证错误: {validation_result.get('errors', [])}\")\n        else:\n            print(f\"   发现组件: {validation_result.get('components', {})}\")\n        \n        return validation_result['valid']\n        \n    except Exception as e:\n        print(f\"❌ 工具函数测试失败: {e}\")\n        return False\n\ndef test_file_structure():\n    \"\"\"测试文件结构\"\"\"\n    print(\"\\n🧪 测试文件结构\")\n    print(\"-\" * 30)\n    \n    required_files = [\n        'bmad_agent_mcp.py',\n        'llm_client.py',\n        'utils.py',\n        'requirements.txt',\n        '.bmad-core/core-config.yaml'\n    ]\n    \n    required_dirs = [\n        '.bmad-core',\n        '.bmad-core/agents',\n        '.bmad-core/workflows',\n        '.bmad-core/templates',\n        '.bmad-core/tasks'\n    ]\n    \n    all_good = True\n    \n    # 检查必需文件\n    print(\"📄 检查必需文件:\")\n    for file_path in required_files:\n        if Path(file_path).exists():\n            print(f\"   ✅ {file_path}\")\n        else:\n            print(f\"   ❌ {file_path} (缺失)\")\n            all_good = False\n    \n    # 检查必需目录\n    print(\"\\n📁 检查必需目录:\")\n    for dir_path in required_dirs:\n        if Path(dir_path).exists():\n            print(f\"   ✅ {dir_path}\")\n        else:\n            print(f\"   ❌ {dir_path} (缺失)\")\n            all_good = False\n    \n    # 检查智能体文件\n    agents_dir = Path('.bmad-core/agents')\n    if agents_dir.exists():\n        agent_files = list(agents_dir.glob('*.md'))\n        print(f\"\\n🤖 智能体文件: {len(agent_files)} 个\")\n        for agent_file in agent_files[:5]:\n            print(f\"   - {agent_file.name}\")\n        if len(agent_files) > 5:\n            print(f\"   ... 还有 {len(agent_files) - 5} 个\")\n    \n    # 检查工作流程文件\n    workflows_dir = Path('.bmad-core/workflows')\n    if workflows_dir.exists():\n        workflow_files = list(workflows_dir.glob('*.yaml'))\n        print(f\"\\n📋 工作流程文件: {len(workflow_files)} 个\")\n        for workflow_file in workflow_files:\n            print(f\"   - {workflow_file.name}\")\n    \n    return all_good\n\ndef test_imports():\n    \"\"\"测试模块导入\"\"\"\n    print(\"\\n🧪 测试模块导入\")\n    print(\"-\" * 30)\n    \n    modules_to_test = [\n        ('fastmcp', 'FastMCP 框架'),\n        ('yaml', 'YAML 解析'),\n        ('pathlib', 'Path 操作'),\n        ('json', 'JSON 处理'),\n        ('os', '操作系统接口'),\n        ('sys', '系统接口'),\n        ('logging', '日志记录')\n    ]\n    \n    all_good = True\n    \n    for module_name, description in modules_to_test:\n        try:\n            __import__(module_name)\n            print(f\"   ✅ {module_name} ({description})\")\n        except ImportError as e:\n            print(f\"   ❌ {module_name} ({description}) - {e}\")\n            all_good = False\n    \n    return all_good\n\ndef main():\n    \"\"\"主测试函数\"\"\"\n    print(\"🚀 BMAD Agent FastMCP Service 测试\")\n    print(\"=\" * 50)\n    \n    tests = [\n        (\"文件结构\", test_file_structure),\n        (\"模块导入\", test_imports),\n        (\"工具函数\", test_utils),\n        (\"LLM 客户端\", test_llm_client),\n        (\"BMAD 核心\", test_bmad_core)\n    ]\n    \n    results = []\n    \n    for test_name, test_func in tests:\n        try:\n            result = test_func()\n            results.append((test_name, result))\n        except Exception as e:\n            print(f\"\\n❌ 测试 '{test_name}' 出现异常: {e}\")\n            results.append((test_name, False))\n    \n    # 总结\n    print(\"\\n\" + \"=\" * 50)\n    print(\"📊 测试结果总结\")\n    print(\"-\" * 30)\n    \n    passed = 0\n    total = len(results)\n    \n    for test_name, result in results:\n        status = \"✅ 通过\" if result else \"❌ 失败\"\n        print(f\"   {test_name}: {status}\")\n        if result:\n            passed += 1\n    \n    print(f\"\\n🎯 总体结果: {passed}/{total} 测试通过\")\n    \n    if passed == total:\n        print(\"🎉 所有测试都通过了！BMAD Agent 服务已准备就绪。\")\n        return 0\n    else:\n        print(\"⚠️  部分测试失败，请检查配置和依赖。\")\n        return 1\n\nif __name__ == \"__main__\":\n    sys.exit(main())"