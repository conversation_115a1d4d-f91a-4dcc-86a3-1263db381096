#!/usr/bin/env python3\n\"\"\"\nBMAD Agent 简化测试版本\n\"\"\"\n\nimport os\nimport json\nfrom typing import Dict, Any, Optional\nfrom fastmcp import FastMCP\n\n# 初始化 FastMCP 应用\nmcp = FastMCP(\"BMAD Agent Test Service\")\n\n# LLM 配置\nUSE_BUILTIN_LLM = os.getenv(\"USE_BUILTIN_LLM\", \"true\").lower() == \"true\"\n\<EMAIL>()\ndef test_connection() -> Dict[str, Any]:\n    \"\"\"\n    测试 BMAD 服务连接\n    \n    Returns:\n        Dict[str, Any]: 连接状态信息\n    \"\"\"\n    return {\n        \"success\": True,\n        \"service\": \"BMAD Agent Test Service\",\n        \"mode\": \"builtin_llm\" if USE_BUILTIN_LLM else \"external_api\",\n        \"status\": \"connected\",\n        \"message\": \"🎉 BMAD 服务连接成功！\"\n    }\n\<EMAIL>()\ndef test_agents() -> Dict[str, Any]:\n    \"\"\"\n    测试智能体功能\n    \n    Returns:\n        Dict[str, Any]: 智能体测试结果\n    \"\"\"\n    test_agents = [\n        {\"id\": \"pm\", \"title\": \"产品经理\", \"icon\": \"👔\"},\n        {\"id\": \"dev\", \"title\": \"全栈开发者\", \"icon\": \"💻\"},\n        {\"id\": \"analyst\", \"title\": \"业务分析师\", \"icon\": \"📊\"}\n    ]\n    \n    return {\n        \"success\": True,\n        \"agents_count\": len(test_agents),\n        \"agents\": test_agents,\n        \"message\": \"✅ 智能体测试通过\"\n    }\n\<EMAIL>()\ndef test_llm_mode() -> Dict[str, Any]:\n    \"\"\"\n    测试 LLM 模式\n    \n    Returns:\n        Dict[str, Any]: LLM 模式测试结果\n    \"\"\"\n    current_mode = \"builtin_llm\" if USE_BUILTIN_LLM else \"external_api\"\n    \n    return {\n        \"success\": True,\n        \"current_mode\": current_mode,\n        \"builtin_available\": True,\n        \"external_available\": bool(os.getenv(\"DEEPSEEK_API_KEY\")),\n        \"message\": f\"✅ 当前使用 {current_mode} 模式\"\n    }\n\<EMAIL>()\ndef test_workflows() -> Dict[str, Any]:\n    \"\"\"\n    测试工作流程功能\n    \n    Returns:\n        Dict[str, Any]: 工作流程测试结果\n    \"\"\"\n    test_workflows = [\n        {\"id\": \"greenfield-fullstack\", \"name\": \"全栈开发（新项目）\"},\n        {\"id\": \"brownfield-fullstack\", \"name\": \"全栈开发（现有项目）\"},\n        {\"id\": \"greenfield-service\", \"name\": \"服务开发（新项目）\"}\n    ]\n    \n    return {\n        \"success\": True,\n        \"workflows_count\": len(test_workflows),\n        \"workflows\": test_workflows,\n        \"message\": \"✅ 工作流程测试通过\"\n    }\n\<EMAIL>()\ndef test_templates() -> Dict[str, Any]:\n    \"\"\"\n    测试模板功能\n    \n    Returns:\n        Dict[str, Any]: 模板测试结果\n    \"\"\"\n    test_templates = [\n        {\"name\": \"prd-template\", \"description\": \"产品需求文档模板\"},\n        {\"name\": \"architecture-template\", \"description\": \"系统架构文档模板\"},\n        {\"name\": \"story-template\", \"description\": \"用户故事模板\"}\n    ]\n    \n    return {\n        \"success\": True,\n        \"templates_count\": len(test_templates),\n        \"templates\": test_templates,\n        \"message\": \"✅ 模板测试通过\"\n    }\n\<EMAIL>()\ndef run_full_test() -> Dict[str, Any]:\n    \"\"\"\n    运行完整测试套件\n    \n    Returns:\n        Dict[str, Any]: 完整测试结果\n    \"\"\"\n    test_results = []\n    \n    # 测试连接\n    try:\n        connection_result = test_connection()\n        test_results.append({\"test\": \"connection\", \"status\": \"✅ 通过\", \"details\": connection_result})\n    except Exception as e:\n        test_results.append({\"test\": \"connection\", \"status\": \"❌ 失败\", \"error\": str(e)})\n    \n    # 测试智能体\n    try:\n        agents_result = test_agents()\n        test_results.append({\"test\": \"agents\", \"status\": \"✅ 通过\", \"details\": agents_result})\n    except Exception as e:\n        test_results.append({\"test\": \"agents\", \"status\": \"❌ 失败\", \"error\": str(e)})\n    \n    # 测试 LLM 模式\n    try:\n        llm_result = test_llm_mode()\n        test_results.append({\"test\": \"llm_mode\", \"status\": \"✅ 通过\", \"details\": llm_result})\n    except Exception as e:\n        test_results.append({\"test\": \"llm_mode\", \"status\": \"❌ 失败\", \"error\": str(e)})\n    \n    # 测试工作流程\n    try:\n        workflows_result = test_workflows()\n        test_results.append({\"test\": \"workflows\", \"status\": \"✅ 通过\", \"details\": workflows_result})\n    except Exception as e:\n        test_results.append({\"test\": \"workflows\", \"status\": \"❌ 失败\", \"error\": str(e)})\n    \n    # 测试模板\n    try:\n        templates_result = test_templates()\n        test_results.append({\"test\": \"templates\", \"status\": \"✅ 通过\", \"details\": templates_result})\n    except Exception as e:\n        test_results.append({\"test\": \"templates\", \"status\": \"❌ 失败\", \"error\": str(e)})\n    \n    # 统计结果\n    passed_tests = sum(1 for result in test_results if \"✅\" in result[\"status\"])\n    total_tests = len(test_results)\n    \n    return {\n        \"success\": passed_tests == total_tests,\n        \"summary\": f\"{passed_tests}/{total_tests} 测试通过\",\n        \"test_results\": test_results,\n        \"message\": \"🎉 所有测试通过！\" if passed_tests == total_tests else \"⚠️ 部分测试失败\"\n    }\n\<EMAIL>()\ndef get_system_info() -> Dict[str, Any]:\n    \"\"\"\n    获取系统信息\n    \n    Returns:\n        Dict[str, Any]: 系统信息\n    \"\"\"\n    return {\n        \"service_name\": \"BMAD Agent Test Service\",\n        \"version\": \"1.0.0\",\n        \"python_version\": f\"{os.sys.version_info.major}.{os.sys.version_info.minor}.{os.sys.version_info.micro}\",\n        \"environment\": {\n            \"USE_BUILTIN_LLM\": os.getenv(\"USE_BUILTIN_LLM\", \"true\"),\n            \"DEEPSEEK_API_KEY\": \"已配置\" if os.getenv(\"DEEPSEEK_API_KEY\") else \"未配置\",\n            \"PYTHONPATH\": os.getenv(\"PYTHONPATH\", \"未设置\")\n        },\n        \"features\": [\n            \"智能体管理\",\n            \"工作流程控制\",\n            \"双 LLM 模式\",\n            \"模板系统\",\n            \"MCP 协议支持\"\n        ],\n        \"status\": \"运行中\",\n        \"message\": \"📊 系统信息获取成功\"\n    }\n\n# 主函数\nif __name__ == \"__main__\":\n    print(\"🚀 启动 BMAD Agent 测试服务...\")\n    print(f\"📍 当前 LLM 模式: {'内置模式' if USE_BUILTIN_LLM else '外部 API 模式'}\")\n    print(\"🔧 可用测试工具:\")\n    print(\"   - test_connection(): 测试服务连接\")\n    print(\"   - test_agents(): 测试智能体功能\")\n    print(\"   - test_llm_mode(): 测试 LLM 模式\")\n    print(\"   - test_workflows(): 测试工作流程\")\n    print(\"   - test_templates(): 测试模板功能\")\n    print(\"   - run_full_test(): 运行完整测试\")\n    print(\"   - get_system_info(): 获取系统信息\")\n    print(\"\\n✅ 测试服务已准备就绪！\")\n    \n    # 启动 FastMCP 服务\n    mcp.run()"