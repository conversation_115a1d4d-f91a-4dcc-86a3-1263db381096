"""
Small end-to-end example:
- Creates a simple example module under tests/_examples_
- Invokes TestGenerator to generate tests for that module
- Uses TestExecutor to run generated tests with coverage and prints results
"""
from pathlib import Path
import sys
import json
import logging

# ensure project root is on sys.path
PROJECT_ROOT = Path(__file__).resolve().parents[1]
sys.path.insert(0, str(PROJECT_ROOT))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("example")

from workflow.code_generator import TestGenerator
from workflow.test_executor import TestExecutor

# Create a simple example module to generate tests for
examples_dir = PROJECT_ROOT / "tests" / "_examples"
examples_dir.mkdir(parents=True, exist_ok=True)
example_file = examples_dir / "sample_module.py"
example_code = '''"""
Example module for TestGenerator demo
"""

def add(a, b):
    """Return sum of a and b"""
    return a + b

def divide(a, b):
    """Divide; raises on zero"""
    if b == 0:
        raise ValueError("division by zero")
    return a / b
'''
example_file.write_text(example_code, encoding="utf-8")
logger.info(f"Wrote example module: {example_file}")

# Run TestGenerator
tg = TestGenerator(str(PROJECT_ROOT))
generated = tg.generate_tests([str(example_file.relative_to(PROJECT_ROOT))])

# Print generation summary
print("=== Test Generation Result ===")
print(json.dumps(generated.to_dict() if hasattr(generated, "to_dict") else generated.__dict__, indent=2, ensure_ascii=False))

# If tests were generated, run them with TestExecutor
if generated.test_files:
    te = TestExecutor()
    # resolve paths
    test_paths = [str(PROJECT_ROOT / tf) for tf in generated.test_files]
    # run with coverage output to coverage.xml in project root
    cov_path = str(PROJECT_ROOT / "coverage.xml")
    exec_result = te.run_tests_with_coverage(test_paths, cov_report_path=cov_path, cwd=str(PROJECT_ROOT))
    print("=== Test Execution Result ===")
    print("success:", exec_result.success)
    print("return_code:", exec_result.return_code)
    print("stdout (truncated):")
    print(exec_result.stdout[:1000])
    if exec_result.junit_report.get("coverage_xml"):
        print("coverage xml length:", len(exec_result.junit_report["coverage_xml"]))
    else:
        # try read coverage.xml if present on disk
        cov_file = PROJECT_ROOT / "coverage.xml"
        if cov_file.exists():
            print("coverage.xml exists at", str(cov_file))
else:
    print("No tests were generated.")