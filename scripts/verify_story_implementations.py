#!/usr/bin/env python3
"""
验证所有Story实现状态的脚本
检查实际代码实现与story文档的匹配度
"""

import os
import sys
from pathlib import Path
import importlib.util
import inspect
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_module_exists(module_path: str) -> bool:
    """检查模块是否存在"""
    return Path(module_path).exists()

def check_class_exists(module_path: str, class_name: str) -> bool:
    """检查类是否存在"""
    try:
        if not Path(module_path).exists():
            return False
        spec = importlib.util.spec_from_file_location("module", module_path)
        if spec is None:
            return False
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return hasattr(module, class_name)
    except Exception as e:
        print(f"Error checking {class_name} in {module_path}: {e}")
        return False

def count_mcp_tools() -> int:
    """统计MCP工具数量"""
    try:
        mcp_file = project_root / "bmad_agent_mcp.py"
        with open(mcp_file, 'r', encoding='utf-8') as f:
            content = f.read()
        return content.count("@mcp.tool()")
    except Exception:
        return 0

def check_test_files() -> Dict[str, bool]:
    """检查测试文件"""
    test_files = {
        "unit_tests": (project_root / "tests" / "unit" / "workflow").exists(),
        "integration_tests": (project_root / "tests" / "integration").exists(),
        "e2e_workflow_test": (project_root / "tests" / "integration" / "test_e2e_workflow.py").exists(),
    }
    return test_files

def verify_story_1_1() -> Dict[str, Any]:
    """验证Story 1.1: 工作流程状态检测系统"""
    results = {
        "story_id": "1.1",
        "title": "工作流程状态检测系统",
        "status": "unknown",
        "components": {},
        "mcp_tools": 0,
        "tests": {}
    }
    
    # 检查核心组件
    workflow_models = project_root / "workflow" / "models.py"
    state_engine = project_root / "workflow" / "state_engine.py"
    
    results["components"]["WorkflowState"] = check_class_exists(str(workflow_models), "WorkflowState")
    results["components"]["state_engine"] = check_module_exists(str(state_engine))
    results["mcp_tools"] = count_mcp_tools()
    results["tests"] = check_test_files()
    
    # 判断完成状态
    if (results["components"]["WorkflowState"] and 
        results["components"]["state_engine"] and 
        results["mcp_tools"] > 0):
        results["status"] = "完全实现"
    else:
        results["status"] = "部分实现"
    
    return results

def verify_story_1_2() -> Dict[str, Any]:
    """验证Story 1.2: 文档自动分片功能"""
    results = {
        "story_id": "1.2", 
        "title": "文档自动分片功能",
        "status": "unknown",
        "components": {},
        "tests": {}
    }
    
    doc_processor = project_root / "workflow" / "document_processor.py"
    results["components"]["document_processor"] = check_module_exists(str(doc_processor))
    results["components"]["ShardingResult"] = check_class_exists(str(doc_processor), "ShardingResult")
    results["tests"] = check_test_files()
    
    if (results["components"]["document_processor"] and 
        results["components"]["ShardingResult"]):
        results["status"] = "完全实现"
    else:
        results["status"] = "部分实现"
    
    return results

def verify_story_2_1() -> Dict[str, Any]:
    """验证Story 2.1: 智能体任务传递机制"""
    results = {
        "story_id": "2.1",
        "title": "智能体任务传递机制", 
        "status": "unknown",
        "components": {},
        "tests": {}
    }
    
    orchestrator = project_root / "workflow" / "agent_orchestrator.py"
    models = project_root / "workflow" / "models.py"
    
    results["components"]["AgentOrchestrator"] = check_class_exists(str(orchestrator), "AgentOrchestrator")
    results["components"]["AgentSession"] = check_class_exists(str(models), "AgentSession")
    results["components"]["TaskExecution"] = check_class_exists(str(models), "TaskExecution")
    results["tests"] = check_test_files()
    
    if all(results["components"].values()):
        results["status"] = "完全实现"
    else:
        results["status"] = "部分实现"
    
    return results

def verify_story_2_2() -> Dict[str, Any]:
    """验证Story 2.2: 上下文自动加载系统"""
    results = {
        "story_id": "2.2",
        "title": "上下文自动加载系统",
        "status": "unknown", 
        "components": {},
        "tests": {}
    }
    
    context_loader = project_root / "workflow" / "context_loader.py"
    models = project_root / "workflow" / "models.py"
    
    results["components"]["ContextLoader"] = check_class_exists(str(context_loader), "ContextLoader")
    results["components"]["SharedContext"] = check_class_exists(str(models), "SharedContext")
    results["tests"] = check_test_files()
    
    if all(results["components"].values()):
        results["status"] = "完全实现"
    else:
        results["status"] = "部分实现"
    
    return results

def main():
    """主函数"""
    print("🔍 验证Story实现状态")
    print("=" * 50)
    
    # 验证各个Story
    stories = [
        verify_story_1_1(),
        verify_story_1_2(), 
        verify_story_2_1(),
        verify_story_2_2()
    ]
    
    # 输出结果
    for story in stories:
        status_icon = "✅" if story["status"] == "完全实现" else "⚠️"
        print(f"\n{status_icon} Story {story['story_id']}: {story['title']}")
        print(f"   状态: {story['status']}")
        
        if "components" in story:
            print("   组件:")
            for comp, exists in story["components"].items():
                comp_icon = "✅" if exists else "❌"
                print(f"     {comp_icon} {comp}")
        
        if "mcp_tools" in story and story["mcp_tools"] > 0:
            print(f"   MCP工具: {story['mcp_tools']}个")
        
        if "tests" in story:
            test_count = sum(1 for v in story["tests"].values() if v)
            print(f"   测试: {test_count}/{len(story['tests'])}个测试类型存在")
    
    print(f"\n📊 总体统计:")
    completed = sum(1 for s in stories if s["status"] == "完全实现")
    print(f"   完全实现: {completed}/{len(stories)} ({completed/len(stories)*100:.1f}%)")
    print(f"   MCP工具总数: {count_mcp_tools()}个")
    
    return stories

if __name__ == "__main__":
    main()
