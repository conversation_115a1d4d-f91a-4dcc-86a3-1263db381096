#!/bin/bash

# 代码质量检查脚本
# 运行 black, flake8, mypy 和测试覆盖率检查

set -e

echo "🔍 开始代码质量检查..."
echo "================================"

# 创建报告目录
mkdir -p reports

# 1. Black 格式检查
echo "📝 运行 Black 格式检查..."
if black --check --diff . > reports/black-report.txt 2>&1; then
    echo "✅ Black 格式检查通过"
else
    echo "❌ Black 格式检查失败，查看 reports/black-report.txt"
    cat reports/black-report.txt
fi

# 2. Flake8 代码检查
echo ""
echo "🔍 运行 Flake8 代码检查..."
if flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics > reports/flake8-errors.txt 2>&1; then
    echo "✅ Flake8 严重错误检查通过"
else
    echo "❌ Flake8 发现严重错误，查看 reports/flake8-errors.txt"
    cat reports/flake8-errors.txt
fi

# Flake8 完整检查（警告级别）
flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics > reports/flake8-full.txt 2>&1
echo "📊 Flake8 完整报告已生成：reports/flake8-full.txt"

# 3. MyPy 类型检查
echo ""
echo "🔍 运行 MyPy 类型检查..."
if mypy . --ignore-missing-imports --show-error-codes > reports/mypy-report.txt 2>&1; then
    echo "✅ MyPy 类型检查通过"
else
    echo "⚠️  MyPy 发现类型问题，查看 reports/mypy-report.txt"
    cat reports/mypy-report.txt
fi

# 4. 测试覆盖率
echo ""
echo "🧪 运行测试并生成覆盖率报告..."
if python3 -m pytest tests/ --cov=workflow --cov=bmad_agent_mcp --cov-report=html --cov-report=xml --cov-report=term > reports/test-coverage.txt 2>&1; then
    echo "✅ 测试通过，覆盖率报告已生成"
    echo "📊 HTML 覆盖率报告：htmlcov/index.html"
    echo "📊 XML 覆盖率报告：coverage.xml"
else
    echo "❌ 测试失败，查看 reports/test-coverage.txt"
    cat reports/test-coverage.txt
fi

# 5. 生成综合报告
echo ""
echo "📋 生成综合质量报告..."
cat > reports/quality-summary.md << EOF
# 代码质量检查报告

生成时间: $(date)

## Black 格式检查
$(if black --check . >/dev/null 2>&1; then echo "✅ 通过"; else echo "❌ 失败"; fi)

## Flake8 代码检查
$(if flake8 . --count --select=E9,F63,F7,F82 >/dev/null 2>&1; then echo "✅ 无严重错误"; else echo "❌ 发现严重错误"; fi)

## MyPy 类型检查
$(if mypy . --ignore-missing-imports >/dev/null 2>&1; then echo "✅ 通过"; else echo "⚠️ 发现类型问题"; fi)

## 测试覆盖率
$(python3 -m pytest tests/ --cov=workflow --cov=bmad_agent_mcp --cov-report=term-missing | grep "TOTAL" || echo "无法获取覆盖率信息")

## 详细报告文件
- Black: reports/black-report.txt
- Flake8: reports/flake8-full.txt
- MyPy: reports/mypy-report.txt
- 测试: reports/test-coverage.txt
- HTML覆盖率: htmlcov/index.html
EOF

echo "✅ 综合质量报告已生成：reports/quality-summary.md"

# 6. 显示摘要
echo ""
echo "📊 质量检查摘要："
echo "================================"
cat reports/quality-summary.md

echo ""
echo "🎉 代码质量检查完成！"
echo "查看详细报告请访问 reports/ 目录"
