"""
Small helper to run shard_documents on docs/prd.md and print results.
Run: python scripts/run_shard_prd.py
"""
# Ensure the repository root is on sys.path so local packages (workflow) can be imported
import sys
from pathlib import Path
ROOT = Path(__file__).resolve().parents[1]
if str(ROOT) not in sys.path:
    sys.path.insert(0, str(ROOT))

from workflow.document_processor import shard_documents
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    src = Path("docs/prd.md")
    if not src.exists():
        logger.error("Source file not found: %s", src)
        sys.exit(2)

    result = shard_documents(str(src))
    if result.success:
        logger.info("Sharding completed successfully.")
        logger.info("Index file: %s", result.index_file)
        logger.info("Generated files:")
        for f in result.sharded_files:
            logger.info(" - %s", f)
        sys.exit(0)
    else:
        logger.error("Sharding failed with errors: %s", result.errors)
        sys.exit(1)

if __name__ == "__main__":
    main()