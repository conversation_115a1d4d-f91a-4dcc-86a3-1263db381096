name: Code Quality Checks

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  code-quality:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install black flake8 mypy pytest pytest-cov
        pip install -r requirements.txt
    
    - name: Run Black (Code Formatting Check)
      run: |
        black --check --diff .
        echo "Black formatting check completed"
    
    - name: Run Flake8 (Linting)
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
        echo "Flake8 linting completed"
    
    - name: Run MyPy (Type Checking)
      run: |
        mypy . --ignore-missing-imports --show-error-codes
        echo "MyPy type checking completed"
    
    - name: Run Tests with Coverage
      run: |
        python -m pytest tests/ --cov=workflow --cov=bmad_agent_mcp --cov-report=xml --cov-report=html --cov-report=term-missing
        echo "Tests with coverage completed"
    
    - name: Upload Coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella
        fail_ci_if_error: false
    
    - name: Generate Code Quality Report
      run: |
        echo "# Code Quality Report" > code-quality-report.md
        echo "" >> code-quality-report.md
        echo "## Black Formatting" >> code-quality-report.md
        black --check . && echo "✅ All files properly formatted" >> code-quality-report.md || echo "❌ Formatting issues found" >> code-quality-report.md
        echo "" >> code-quality-report.md
        echo "## Flake8 Linting" >> code-quality-report.md
        flake8 . --count --statistics >> code-quality-report.md || true
        echo "" >> code-quality-report.md
        echo "## MyPy Type Checking" >> code-quality-report.md
        mypy . --ignore-missing-imports >> code-quality-report.md || true
        echo "" >> code-quality-report.md
        echo "## Test Coverage" >> code-quality-report.md
        python -m pytest tests/ --cov=workflow --cov=bmad_agent_mcp --cov-report=term >> code-quality-report.md || true
    
    - name: Upload Code Quality Report
      uses: actions/upload-artifact@v3
      with:
        name: code-quality-report
        path: |
          code-quality-report.md
          htmlcov/
          coverage.xml
    
    - name: Comment PR with Quality Report
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('code-quality-report.md', 'utf8');
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: report
          });

  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install security tools
      run: |
        python -m pip install --upgrade pip
        pip install bandit safety
    
    - name: Run Bandit (Security Linting)
      run: |
        bandit -r . -f json -o bandit-report.json || true
        bandit -r . || true
    
    - name: Run Safety (Dependency Security Check)
      run: |
        safety check --json --output safety-report.json || true
        safety check || true
    
    - name: Upload Security Reports
      uses: actions/upload-artifact@v3
      with:
        name: security-reports
        path: |
          bandit-report.json
          safety-report.json
